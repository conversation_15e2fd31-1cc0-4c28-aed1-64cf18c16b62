"""
基于FFmpeg的视频合成器
替代MoviePy以提升处理速度和减少依赖
"""

import os
import sys
import json
import subprocess
import tempfile
import threading
import time
import re
import shutil
import platform
from pathlib import Path
from datetime import datetime

# 导入进程管理器
try:
    from .core import video_ffmpeg_manager
except ImportError:
    # 如果导入失败，创建一个简单的占位符
    class DummyManager:
        def register_process(self, process): pass
        def unregister_process(self, process): pass
    video_ffmpeg_manager = DummyManager()

def create_managed_ffmpeg_process(cmd):
    """创建一个被管理的FFmpeg进程"""
    if platform.system() == "Windows":
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8', errors='ignore',
                                 creationflags=subprocess.CREATE_NO_WINDOW)
    else:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8', errors='ignore')

    # 注册进程到管理器
    video_ffmpeg_manager.register_process(process)
    return process

def cleanup_managed_process(process):
    """清理被管理的FFmpeg进程"""
    if process:
        video_ffmpeg_manager.unregister_process(process)


def get_long_path(path):
    """获取Windows长路径格式"""
    if os.name == 'nt' and not path.startswith('\\\\?\\'):
        return f'\\\\?\\{os.path.abspath(path)}'
    return path


class FFmpegVideoComposer:
    """基于FFmpeg的视频合成器"""

    def __init__(self):
        self.should_stop = False
        self.progress_callback = None

    def is_available(self):
        """检查FFmpeg是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        return ffmpeg_exe is not None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def _get_ffmpeg_executable(self):
        """获取FFmpeg可执行文件路径"""
        # 尝试从配置文件获取FFmpeg路径
        config_files = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config.json"),
            "config.json"
        ]

        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    if 'ffmpeg_path' in config and config['ffmpeg_path']:
                        ffmpeg_dir = config['ffmpeg_path']

                        # 尝试多种可能的路径
                        possible_paths = [
                            os.path.join(ffmpeg_dir, "ffmpeg.exe"),
                            os.path.join(ffmpeg_dir, "bin", "ffmpeg.exe"),
                            ffmpeg_dir if ffmpeg_dir.endswith('ffmpeg.exe') else None
                        ]

                        for path in possible_paths:
                            if path and os.path.exists(path):
                                return path
                except Exception:
                    continue

        # 尝试系统PATH
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                return 'ffmpeg'
        except Exception:
            pass

        return None

    def get_media_duration(self, file_path):
        """获取媒体文件时长（秒）"""
        ffprobe_exe = self._get_ffprobe_executable()
        if not ffprobe_exe:
            raise Exception("FFprobe不可用")

        cmd = [
            ffprobe_exe, '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            file_path
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', check=True)
            return float(result.stdout.strip())
        except Exception as e:
            raise Exception(f"获取媒体时长失败: {e}")

    def _get_ffprobe_executable(self):
        """获取FFprobe可执行文件路径"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return None

        if ffmpeg_exe == 'ffmpeg':
            return 'ffprobe'
        else:
            # 替换ffmpeg为ffprobe
            return ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')

    def loop_video_to_duration(self, video_path, target_duration, output_path, settings=None, progress_callback=None):
        """将视频循环到指定时长"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 计算需要循环的次数
        loop_count = max(1, int(target_duration / video_duration) + 1)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，目标: {target_duration:.2f}秒")
            progress_callback(f"需要循环 {loop_count} 次")

        # 检查是否需要在循环时应用编码设置
        need_encoding = self._need_video_encoding_for_loop(settings)

        if progress_callback:
            if need_encoding:
                progress_callback(f"🔍 使用编码循环模式（会应用编码设置）")
            else:
                progress_callback(f"🔍 使用简单拼接模式（直接复制，最快）")

        if need_encoding:
            # 需要编码的循环（应用码率等设置）
            result = self._loop_video_with_encoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)
        else:
            # 简单的复制循环（最快）
            result = self._loop_video_simple_concat(video_path, target_duration, output_path, loop_count, settings, progress_callback)

        # 验证循环结果
        if result and result != "USER_STOPPED" and os.path.exists(output_path):
            try:
                actual_duration = self.get_media_duration(output_path)
                if progress_callback:
                    progress_callback(f"🔍 循环完成，实际时长: {actual_duration:.2f}秒，目标时长: {target_duration:.2f}秒")
                    if abs(actual_duration - target_duration) > 5:  # 如果差异超过5秒
                        progress_callback(f"⚠️ 警告：循环视频时长与目标时长差异较大！差异: {abs(actual_duration - target_duration):.2f}秒")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"⚠️ 获取循环视频时长失败: {e}")

        return result

    def _loop_video_simple_concat(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用简单的concat方法循环视频 - 直接复制拼接"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 优先尝试使用stream_loop方法（更快）
        if self._try_stream_loop_method(video_path, target_duration, output_path, progress_callback):
            return True

        # 如果stream_loop失败，回退到concat方法
        if progress_callback:
            progress_callback("stream_loop方法失败，使用concat方法...")

        # 创建临时concat文件
        concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')

        try:
            # 写入concat文件内容 - 就是把同一个视频文件重复几次
            for i in range(loop_count):
                # 使用绝对路径，避免路径问题
                abs_video_path = os.path.abspath(video_path).replace('\\', '/')
                concat_file.write(f"file '{abs_video_path}'\n")
            concat_file.close()

            if progress_callback:
                progress_callback(f"创建concat文件，重复 {loop_count} 次")

            # 创建临时进度文件
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 使用concat demuxer直接拼接，添加时间戳修复参数
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-c', 'copy',  # 直接复制，不重新编码（最快）
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成时间戳
                '-max_muxing_queue_size', '1024',  # 增加缓冲区
            ]

            # 只有当目标时长小于总循环时长时才添加-t参数
            total_loop_duration = video_duration * loop_count
            if target_duration < total_loop_duration:
                cmd.extend(['-t', str(target_duration)])
                if progress_callback:
                    progress_callback(f"裁剪到目标时长: {target_duration:.2f}秒")
            else:
                if progress_callback:
                    progress_callback(f"保持完整时长: {total_loop_duration:.2f}秒")

            cmd.append(output_path)

            if progress_callback:
                progress_callback("正在快速拼接视频...")

            # 启动FFmpeg进程（隐藏窗口）
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file.name, target_duration, progress_callback, "拼接视频")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存FFmpeg命令和输出到调试文件夹（如果可能）
            try:
                output_dir = os.path.dirname(output_path)
                debug_cmd_file = os.path.join(output_dir, "ffmpeg_loop_command.txt")
                with open(debug_cmd_file, 'w', encoding='utf-8') as f:
                    f.write(f"循环视频命令:\n{' '.join(cmd)}\n\n")
                    f.write(f"返回码: {process.returncode}\n\n")
                    f.write(f"标准输出:\n{stdout}\n\n")
                    f.write(f"错误输出:\n{stderr}\n")
            except:
                pass

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("视频循环已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才尝试重新编码
                    if progress_callback:
                        progress_callback(f"快速拼接失败，使用重新编码方式: {stderr}")
                    # 如果直接copy失败，使用重新编码
                    return self._loop_video_with_reencoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            if progress_callback:
                progress_callback("视频循环完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时文件
            try:
                os.unlink(concat_file.name)
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _loop_video_with_reencoding(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用重新编码的方式循环视频（备用方案）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 创建临时concat文件
        concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')

        try:
            # 写入concat文件内容
            for i in range(loop_count):
                abs_video_path = os.path.abspath(video_path).replace('\\', '/')
                concat_file.write(f"file '{abs_video_path}'\n")
            concat_file.close()

            # 创建临时进度文件
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 使用重新编码确保兼容性
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-t', str(target_duration),
                '-c:v', 'libx264',
                '-preset', 'fast',  # 使用fast预设提高速度
                '-crf', '23',  # 合理的质量
                '-pix_fmt', 'yuv420p',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart',
                output_path
            ]

            if progress_callback:
                progress_callback("正在重新编码拼接视频...")

            # 启动FFmpeg进程（隐藏窗口）
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file.name, target_duration, progress_callback, "重编码拼接")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("视频重编码已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    if progress_callback:
                        progress_callback(f"重新编码失败: {stderr}")
                    return False

            if progress_callback:
                progress_callback("视频循环完成")

            return True

        finally:
            # 清理临时文件
            try:
                os.unlink(concat_file.name)
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _loop_video_with_filter(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用filter_complex进行视频循环（最后备用方案）"""
        if progress_callback:
            progress_callback("所有简单方法都失败了，尝试使用filter方法...")

        # 这个方法通常很慢，所以直接返回失败，让用户知道有问题
        if progress_callback:
            progress_callback("Filter方法处理时间较长，建议检查视频文件格式或使用其他视频文件")

        return False
    def _need_video_encoding_for_loop(self, settings):
        """检查循环视频时是否需要编码"""
        if not settings:
            return False

        # 如果启用了自定义分辨率，需要在循环时处理
        if settings.get('custom_resolution', False):
            return True

        # 注意：自定义压缩不需要在循环阶段处理，可以在后续步骤处理
        # 这样可以保持循环阶段的高速度
        # if settings.get('custom_compression', False):
        #     return True

        return False

    def _try_stream_loop_method(self, video_path, target_duration, output_path, progress_callback=None):
        """尝试使用stream_loop方法循环视频（最快的方法）"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()

            if progress_callback:
                progress_callback("使用stream_loop极速循环模式...")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 使用stream_loop方法 - 参考技术文档的推荐方式
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-stream_loop', '-1',  # 无限循环输入
                '-i', video_path,
                '-t', str(target_duration),  # 指定输出时长
                '-c', 'copy',  # 直接复制，不重新编码（最快）
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成时间戳
                '-max_muxing_queue_size', '4096',  # 增大缓冲区提升性能
                '-movflags', '+faststart',  # 优化播放
                output_path
            ]

            if progress_callback:
                progress_callback("启动stream_loop极速处理...")

            # 启动FFmpeg进程
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file.name, target_duration, progress_callback, "极速循环")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 清理临时文件
            try:
                import os
                os.unlink(progress_file.name)
            except Exception:
                pass

            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            if process.returncode == 0:
                if progress_callback:
                    progress_callback("stream_loop极速循环完成")
                return True
            else:
                if progress_callback:
                    progress_callback(f"stream_loop方法失败: {stderr}")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"stream_loop方法异常: {str(e)}")
            return False

    def _get_video_codec_info(self, video_path):
        """获取视频文件的编码信息"""
        try:
            ffprobe_exe = self._get_ffprobe_executable()
            if not ffprobe_exe:
                return None

            cmd = [
                ffprobe_exe, '-v', 'quiet',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name,profile,level,pix_fmt,width,height,r_frame_rate',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                parts = result.stdout.strip().split(',')
                if len(parts) >= 7:
                    return {
                        'codec': parts[0],
                        'profile': parts[1] if parts[1] != 'unknown' else None,
                        'level': parts[2] if parts[2] != 'unknown' else None,
                        'pix_fmt': parts[3] if parts[3] != 'unknown' else None,
                        'width': int(parts[4]) if parts[4].isdigit() else None,
                        'height': int(parts[5]) if parts[5].isdigit() else None,
                        'fps': parts[6] if parts[6] != 'unknown' else None
                    }
        except Exception as e:
            print(f"获取视频编码信息失败: {e}")

        return None

    def _get_ffprobe_executable(self):
        """获取FFprobe可执行文件路径"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return None

        # 构建ffprobe路径
        ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
        if os.path.exists(ffprobe_exe):
            return ffprobe_exe

        # 尝试其他可能的路径
        ffmpeg_dir = os.path.dirname(ffmpeg_exe)
        ffprobe_exe = os.path.join(ffmpeg_dir, 'ffprobe.exe')
        if os.path.exists(ffprobe_exe):
            return ffprobe_exe

        return None



    def _loop_video_with_encoding(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用编码方式循环视频（应用码率等设置）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 创建临时concat文件
        concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')

        try:
            # 写入concat文件内容
            for i in range(loop_count):
                abs_video_path = os.path.abspath(video_path).replace('\\', '/')
                concat_file.write(f"file '{abs_video_path}'\n")
            concat_file.close()

            if progress_callback:
                progress_callback(f"创建concat文件，重复 {loop_count} 次（带编码）")

            # 获取编码器设置
            codec, ffmpeg_params = self._get_optimal_encoder_settings(settings or {})

            # 创建临时进度文件
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 使用concat + 重新编码
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-c:v', codec,
                '-c:a', 'copy'  # 音频直接复制
            ]

            # 只有当目标时长小于总循环时长时才添加-t参数
            total_loop_duration = video_duration * loop_count
            if target_duration < total_loop_duration:
                cmd.extend(['-t', str(target_duration)])
                if progress_callback:
                    progress_callback(f"重新编码并裁剪到: {target_duration:.2f}秒")
            else:
                if progress_callback:
                    progress_callback(f"重新编码保持完整时长: {total_loop_duration:.2f}秒")

            # 添加分辨率设置
            if settings and settings.get('custom_resolution', False):
                width = settings.get('width', 1920)
                height = settings.get('height', 1080)
                cmd.extend(['-vf', f'scale={width}:{height}'])
                if progress_callback:
                    progress_callback(f"应用分辨率设置: {width}x{height}")

            # 添加压缩设置（修复跳转问题）
            if settings and settings.get('custom_compression', False):
                compression_bitrate = settings.get('current_compression_bitrate', 5000)
                cmd.extend(['-b:v', f'{compression_bitrate}k'])

                # 为码率模式添加兼容性参数，修复跳转问题
                if 'nvenc' in codec:
                    cmd.extend([
                        '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                        '-bufsize', f"{compression_bitrate * 2}k",
                        '-rc', 'vbr',
                        '-g', '60',
                        '-keyint_min', '30'
                    ])
                elif 'qsv' in codec:
                    cmd.extend([
                        '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                        '-bufsize', f"{compression_bitrate * 2}k",
                        '-g', '60',
                        '-keyint_min', '30'
                    ])
                elif 'amf' in codec:
                    cmd.extend([
                        '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                        '-bufsize', f"{compression_bitrate * 2}k",
                        '-g', '60',
                        '-keyint_min', '30'
                    ])
                elif codec == 'libx264':
                    cmd.extend([
                        '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                        '-bufsize', f"{compression_bitrate * 2}k",
                        '-g', '60',
                        '-keyint_min', '30',
                        '-profile:v', 'high',
                        '-level', '4.1'
                    ])

                if progress_callback:
                    progress_callback(f"应用兼容性码率设置: {compression_bitrate}k (最大: {int(compression_bitrate * 1.5)}k)")
            else:
                cmd.extend(['-crf', '23'])

                # 为CRF模式也添加关键帧设置
                if 'nvenc' in codec or 'qsv' in codec or 'amf' in codec:
                    cmd.extend(['-g', '60', '-keyint_min', '30'])
                elif codec == 'libx264':
                    cmd.extend(['-g', '60', '-keyint_min', '30', '-profile:v', 'high', '-level', '4.1'])

            # 添加编码器优化参数
            cmd.extend(ffmpeg_params)

            # 添加大幅优化的性能参数
            speed_priority = settings.get('speed_priority', True) if settings else True

            if speed_priority:
                # 速度优先模式 - 最大化性能
                cmd.extend([
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts+flush_packets',
                    '-max_muxing_queue_size', '4096',  # 增大缓冲区
                    '-thread_queue_size', '2048',      # 增大线程队列
                    '-movflags', '+faststart+frag_keyframe+separate_moof',
                    '-flush_packets', '1',             # 立即刷新包
                    '-copyts',                         # 复制时间戳
                    '-start_at_zero',                  # 从零开始
                    '-muxdelay', '0',                  # 无复用延迟
                    '-muxpreload', '0'                 # 无预加载
                ])
            else:
                # 平衡模式
                cmd.extend([
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts',
                    '-max_muxing_queue_size', '2048',
                    '-thread_queue_size', '1024',
                    '-movflags', '+faststart'
                ])

            cmd.append(output_path)

            if progress_callback:
                progress_callback(f"正在编码循环视频... (编码器: {codec})")

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                try:
                    video_duration = self.get_media_duration(video_path) * loop_count
                    self._monitor_ffmpeg_progress(progress_file.name, video_duration, progress_callback, "循环编码")
                except Exception:
                    pass

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("循环编码已停止")
                    return "USER_STOPPED"
                else:
                    raise Exception(f"循环编码失败: {stderr}")

            if progress_callback:
                progress_callback("循环编码完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时文件
            try:
                os.unlink(concat_file.name)
                os.unlink(progress_file.name)
            except Exception:
                pass





    def merge_audio_video(self, video_path, audio_path, output_path, settings=None, progress_callback=None):
        """合并音频和视频，支持BGM混音"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 检查是否启用BGM
        enable_bgm = settings.get('enable_bgm', False) if settings else False

        if enable_bgm:
            if progress_callback:
                progress_callback("正在合并音视频并添加BGM...")
            return self._merge_audio_video_with_bgm(video_path, audio_path, output_path, settings, progress_callback)
        else:
            if progress_callback:
                progress_callback("正在合并音视频...")
            return self._merge_audio_video_simple(video_path, audio_path, output_path, settings, progress_callback)

    def _merge_audio_video_simple(self, video_path, audio_path, output_path, settings=None, progress_callback=None):
        """简单的音视频合并（无BGM）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取音视频时长，使用较短的作为输出时长
        try:
            video_duration = self.get_media_duration(video_path)
            audio_duration = self.get_media_duration(audio_path)
            min_duration = min(video_duration, audio_duration)
        except Exception:
            min_duration = None

        # 获取并行处理设置
        enable_parallel = settings.get('enable_parallel', True) if settings else True
        max_threads = settings.get('max_threads', 4) if settings else 4

        # 检测音频格式，优化编码策略
        audio_codec = self._detect_audio_codec(audio_path)

        # 创建临时进度文件
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        # 获取音视频时长，确保同步
        video_duration = self.get_media_duration(video_path)
        audio_duration = self.get_media_duration(audio_path)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，音频时长: {audio_duration:.2f}秒")

        # 使用较短的时长作为输出时长，避免不同步
        target_duration = min(video_duration, audio_duration)

        # 获取音频合并专用编码器设置
        audio_merge_settings = settings.copy() if settings else {}
        audio_merge_encoder = audio_merge_settings.get('audio_merge_encoder', 'auto')
        if audio_merge_encoder != 'auto':
            audio_merge_settings['encoder'] = audio_merge_encoder

        # 获取编码器设置
        video_codec, video_params = self._get_optimal_encoder_settings(audio_merge_settings)

        if progress_callback:
            progress_callback(f"音频合并使用编码器: {video_codec}")

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,  # 输出进度到文件
            '-i', video_path,
            '-i', audio_path,
            '-t', str(target_duration),  # 明确指定输出时长
            '-c:v', video_codec,  # 使用指定的视频编码器
            '-c:a', 'aac',   # 重新编码音频确保兼容性
            '-b:a', '128k',
            '-ar', '44100',
            '-ac', '2',
            '-map', '0:v:0',  # 明确映射视频流
            '-map', '1:a:0',  # 明确映射音频流
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts'
        ]

        # 添加视频编码参数
        cmd.extend(video_params)

        # 智能音频处理：如果已经是AAC就直接复制，否则快速编码
        if audio_codec and audio_codec.lower() in ['aac', 'mp4a']:
            cmd.extend(['-c:a', 'copy'])  # 直接复制AAC音频，最快
            if progress_callback:
                progress_callback("音频已是AAC格式，直接复制...")
        else:
            cmd.extend(['-c:a', 'aac'])   # 重新编码为AAC
            # 添加快速编码参数
            cmd.extend(['-aac_coder', 'fast'])  # 使用快速AAC编码器

            # 添加线程参数（仅用于音频编码）
            if enable_parallel and max_threads > 0:
                cmd.extend(['-threads', str(max_threads)])

            if progress_callback:
                progress_callback(f"音频格式为{audio_codec}，正在快速编码为AAC...")

        # 如果有明确的时长限制，添加-t参数
        if min_duration:
            cmd.extend(['-t', str(min_duration)])

        cmd.append(output_path)

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and min_duration:
                self._monitor_ffmpeg_progress(progress_file.name, min_duration, progress_callback, "合并音视频")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存FFmpeg命令和输出到调试文件夹（如果可能）
            try:
                output_dir = os.path.dirname(output_path)
                debug_cmd_file = os.path.join(output_dir, "ffmpeg_merge_command.txt")
                with open(debug_cmd_file, 'w', encoding='utf-8') as f:
                    f.write(f"音视频合并命令:\n{' '.join(cmd)}\n\n")
                    f.write(f"返回码: {process.returncode}\n\n")
                    f.write(f"输入视频时长: {video_duration:.2f}秒\n")
                    f.write(f"输入音频时长: {audio_duration:.2f}秒\n")
                    f.write(f"目标时长: {target_duration:.2f}秒\n\n")
                    f.write(f"标准输出:\n{stdout}\n\n")
                    f.write(f"错误输出:\n{stderr}\n")
            except:
                pass

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("音视频合并已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    raise Exception(f"音视频合并失败: {stderr}")

            if progress_callback:
                progress_callback("音视频合并完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _merge_audio_video_with_bgm(self, video_path, audio_path, output_path, settings, progress_callback=None):
        """音视频合并并添加BGM混音"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取BGM设置
        bgm_path = settings.get('bgm_path', '')
        bgm_mode = settings.get('bgm_mode', '单文件循环')
        bgm_volume = settings.get('bgm_volume', 15)  # BGM音量百分比

        if not bgm_path or not os.path.exists(bgm_path):
            raise Exception("BGM路径无效或文件不存在")

        # 选择BGM文件
        selected_bgm = self._select_bgm_file(bgm_path, bgm_mode, progress_callback)
        if not selected_bgm:
            raise Exception("无法选择BGM文件")

        # 获取时长信息
        video_duration = self.get_media_duration(video_path)
        audio_duration = self.get_media_duration(audio_path)
        target_duration = min(video_duration, audio_duration)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，音频时长: {audio_duration:.2f}秒")
            progress_callback(f"BGM文件: {os.path.basename(selected_bgm)}")
            progress_callback(f"BGM音量: {bgm_volume}%")

        # 创建临时进度文件
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        # 计算BGM音量（转换为FFmpeg的音量值）
        bgm_volume_db = self._calculate_bgm_volume(bgm_volume)

        # 获取音频合并专用编码器设置
        audio_merge_settings = settings.copy() if settings else {}
        audio_merge_encoder = audio_merge_settings.get('audio_merge_encoder', 'auto')
        if audio_merge_encoder != 'auto':
            audio_merge_settings['encoder'] = audio_merge_encoder

        # 获取编码器设置
        video_codec, video_params = self._get_optimal_encoder_settings(audio_merge_settings)

        # 使用更简单和稳定的方法：总是忽略视频中的音频，只使用外部音频和BGM
        if progress_callback:
            progress_callback(f"正在混合主音频和BGM... (使用编码器: {video_codec})")

        # 构建优化的FFmpeg命令：忽略视频音频，使用外部音频+BGM混音
        speed_priority = settings.get('speed_priority', True)

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,
            '-i', video_path,      # 输入0: 视频（忽略其音频）
            '-i', audio_path,      # 输入1: 主音频
            '-i', selected_bgm,    # 输入2: BGM
            '-filter_complex',
            f'[1:a]volume=1.0[main_audio];'  # 主音频保持原音量
            f'[2:a]volume={bgm_volume_db},aloop=-1:2e+09[bgm_loop];'  # BGM循环并调整音量
            f'[main_audio][bgm_loop]amix=inputs=2:duration=shortest:normalize=0[mixed_audio]',  # 混音（禁用标准化提升速度）
            '-map', '0:v',         # 映射视频流（只要视频，不要音频）
            '-map', '[mixed_audio]',  # 映射混音后的音频
            '-c:v', video_codec,   # 使用指定的视频编码器
            '-c:a', 'aac',         # 音频编码为AAC
            '-b:a', '128k',
            '-profile:a', 'aac_low',  # 优化音频配置
            '-ar', '44100',
            '-ac', '2',
            '-t', str(target_duration),  # 限制输出时长
        ]

        # 根据速度优先模式添加不同的优化参数
        if speed_priority:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+flush_packets',
                '-max_muxing_queue_size', '4096',
                '-thread_queue_size', '2048',
                '-flush_packets', '1',
                '-copyts',
                '-start_at_zero'
            ])
        else:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts',
                '-max_muxing_queue_size', '2048'
            ])

        # 添加视频编码参数
        cmd.extend(video_params)
        cmd.append(output_path)

        try:
            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file.name, target_duration, progress_callback, "BGM混音")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存调试信息
            try:
                output_dir = os.path.dirname(output_path)
                debug_cmd_file = os.path.join(output_dir, "ffmpeg_bgm_command.txt")
                with open(debug_cmd_file, 'w', encoding='utf-8') as f:
                    f.write(f"BGM混音命令:\n{' '.join(cmd)}\n\n")
                    f.write(f"返回码: {process.returncode}\n\n")
                    f.write(f"视频文件: {video_path}\n")
                    f.write(f"音频文件: {audio_path}\n")
                    f.write(f"BGM文件: {selected_bgm}\n")
                    f.write(f"BGM音量: {bgm_volume}% ({bgm_volume_db})\n")
                    f.write(f"目标时长: {target_duration:.2f}秒\n\n")
                    f.write(f"标准输出:\n{stdout}\n\n")
                    f.write(f"错误输出:\n{stderr}\n")
            except:
                pass

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("BGM混音已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    error_msg = f"BGM混音失败: {stderr}"
                    if "Invalid data found when processing input" in stderr:
                        error_msg += "\n提示: 可能是音频文件格式不兼容，请尝试使用MP3格式的BGM文件"
                    elif "No such filter" in stderr:
                        error_msg += "\n提示: FFmpeg版本可能不支持某些音频滤镜，请更新FFmpeg"
                    elif "does not contain any stream" in stderr:
                        error_msg += "\n提示: 某个输入文件可能损坏或格式不正确"

                    raise Exception(error_msg)

            if progress_callback:
                progress_callback("BGM混音完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _select_bgm_file(self, bgm_path, bgm_mode, progress_callback=None):
        """根据BGM模式选择BGM文件"""
        if bgm_mode == "单文件循环":
            # 单文件模式，直接返回文件路径
            if os.path.isfile(bgm_path):
                return bgm_path
            else:
                raise Exception("BGM文件不存在")

        elif bgm_mode == "文件夹随机":
            # 文件夹随机模式，随机选择一个音频文件
            if not os.path.isdir(bgm_path):
                raise Exception("BGM路径不是有效的文件夹")

            # 支持的音频格式
            audio_extensions = {'.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg', '.wma'}

            # 获取文件夹中的所有音频文件
            audio_files = []
            for file in os.listdir(bgm_path):
                if os.path.splitext(file.lower())[1] in audio_extensions:
                    audio_files.append(os.path.join(bgm_path, file))

            if not audio_files:
                raise Exception("BGM文件夹中没有找到音频文件")

            # 随机选择一个文件
            import random
            selected_file = random.choice(audio_files)

            if progress_callback:
                progress_callback(f"随机选择BGM: {os.path.basename(selected_file)}")

            return selected_file

        else:
            raise Exception(f"不支持的BGM模式: {bgm_mode}")

    def _calculate_bgm_volume(self, volume_percent):
        """计算BGM音量值（转换为FFmpeg的volume滤镜值）"""
        # volume_percent 是0-100的百分比
        # FFmpeg的volume滤镜：1.0 = 100%，0.5 = 50%，2.0 = 200%
        return volume_percent / 100.0

    def _is_user_terminated(self, return_code, stderr):
        """检测是否是用户手动中止"""
        # 首先检查全局停止标志
        if hasattr(self, 'should_stop') and self.should_stop:
            return True

        # 检查进程管理器的停止状态
        try:
            from .core import video_ffmpeg_manager
            if hasattr(video_ffmpeg_manager, 'should_stop') and video_ffmpeg_manager.should_stop:
                return True
        except:
            pass

        # 检查进程管理器的用户停止时间戳
        # 这是一个更可靠的检测方法
        try:
            from .core import video_ffmpeg_manager
            import time
            current_time = time.time()
            if hasattr(video_ffmpeg_manager, '_user_stop_time'):
                # 如果在最近10秒内有用户停止请求，认为是用户中止
                if current_time - video_ffmpeg_manager._user_stop_time < 10:
                    return True
        except:
            pass

        # Windows下进程被终止的返回码通常是1或-1073741510
        # Linux下通常是-15 (SIGTERM) 或 -9 (SIGKILL)
        termination_codes = [1, -1, -15, -9, -1073741510, 1073741510]

        # 检查返回码
        if return_code in termination_codes:
            # 首先检查是否包含明确的错误信息（不是终止）
            error_keywords = [
                "Invalid data found", "No such file", "Permission denied",
                "Cannot open", "Failed to", "Error opening", "Invalid",
                "not found", "No such filter", "does not contain any stream",
                "Conversion failed", "could not find codec"
            ]

            # 如果包含明确的错误信息，不认为是用户终止
            if any(keyword in stderr for keyword in error_keywords):
                return False

            # 进一步检查stderr中的终止信息
            termination_keywords = [
                "Terminated", "terminated", "killed", "Killed",
                "Exiting normally", "received signal", "中断", "终止",
                "Interrupted", "interrupted"
            ]

            if any(keyword in stderr for keyword in termination_keywords):
                return True

            # 如果stderr只包含版本信息，很可能是被终止
            if "ffmpeg version" in stderr and len(stderr.strip()) < 1000:
                return True

            # 如果stderr为空或很短，也可能是被终止
            if len(stderr.strip()) < 50:
                return True

        return False





    def resize_video(self, video_path, resolution, output_path, progress_callback=None):
        """调整视频分辨率"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        if progress_callback:
            progress_callback(f"正在调整分辨率到 {resolution}...")

        # 解析分辨率
        if 'x' in resolution:
            width, height = resolution.split('x')
        else:
            raise Exception(f"无效的分辨率格式: {resolution}")

        # 创建临时进度文件
        import tempfile
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        # 获取输入视频时长用于进度计算
        try:
            video_duration = self.get_media_duration(video_path)
        except Exception:
            video_duration = None

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,  # 输出进度到文件
            '-i', video_path,
            '-vf', f'scale={width}:{height}',
            '-c:a', 'copy',  # 复制音频流
            output_path
        ]

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and video_duration:
                self._monitor_ffmpeg_progress(progress_file.name, video_duration, progress_callback, "分辨率调整")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("分辨率调整已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    raise Exception(f"分辨率调整失败: {stderr}")

            if progress_callback:
                progress_callback("分辨率调整完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                import os
                os.unlink(progress_file.name)
            except Exception:
                pass

    def add_subtitles_ffmpeg(self, video_path, subtitle_path, output_path, settings=None, progress_callback=None, _is_retry=False):
        """使用FFmpeg添加字幕"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 使用传统的FFmpeg字幕硬编码方式
        if progress_callback:
            progress_callback("使用FFmpeg字幕硬编码模式...")

        # 字幕添加时使用专门的字幕编码器设置
        subtitle_settings = settings.copy() if settings else {}

        # 获取字幕专用编码器设置
        subtitle_encoder = subtitle_settings.get('subtitle_encoder', 'auto')
        if subtitle_encoder != 'auto':
            subtitle_settings['encoder'] = subtitle_encoder

        # 获取编码器设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(subtitle_settings)

        # 检查是否是硬件编码器
        is_hardware_encoder = any(hw in codec for hw in ['nvenc', 'qsv', 'amf'])
        use_hardware_for_subtitle = subtitle_settings.get('use_hardware_acceleration', True)

        if is_hardware_encoder and use_hardware_for_subtitle:
            if progress_callback:
                progress_callback(f"正在添加字幕... (使用硬件编码器: {codec})")
        else:
            if progress_callback:
                progress_callback(f"正在添加字幕... (使用软件编码器: {codec})")

        # 参考技术文档：使用简单直接的字幕处理方式
        if progress_callback:
            progress_callback("字幕处理进度: 15% - 准备字幕文件")

        try:
            # 创建安全的字幕文件路径（避免特殊字符和路径问题）
            import tempfile
            import shutil
            import uuid

            temp_dir = tempfile.mkdtemp()
            # 使用唯一名称避免冲突
            unique_subtitle_name = f'temp_subtitle_{uuid.uuid4().hex[:8]}.srt'
            safe_subtitle_path = os.path.join(temp_dir, unique_subtitle_name)
            shutil.copy2(subtitle_path, safe_subtitle_path)

            if progress_callback:
                progress_callback("字幕处理进度: 20% - 字幕文件准备完成")

            # 获取视频时长用于进度计算
            try:
                video_duration = self.get_media_duration(video_path)
            except Exception:
                video_duration = None

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = settings.get('max_threads', 4) if settings else 4

            # 创建临时进度文件
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 使用用户设置的编码器，而不是写死
            subtitle_codec = codec
            subtitle_params = ffmpeg_params.copy()

            # 为软件编码器添加优化参数
            if codec == 'libx264':
                subtitle_params.extend([
                    '-tune', 'zerolatency',  # 零延迟
                    '-x264opts', 'no-scenecut:no-mbtree:no-mixed-refs'  # 禁用耗时特性
                ])

            # 字幕处理优化：根据编码器类型显示相应的优化信息
            # 注意：字幕渲染(subtitles滤镜)本身不支持多线程，但编码器可以使用多线程
            # 只为软件编码器添加线程参数，硬件编码器不需要
            if enable_parallel and max_threads > 0 and codec == 'libx264':
                subtitle_params.extend(['-threads', str(max_threads)])
                if progress_callback:
                    progress_callback(f"字幕处理使用极速渲染模式 (软件编码器: {max_threads} 线程)")
            elif progress_callback:
                if any(hw in codec for hw in ['nvenc', 'qsv', 'amf']):
                    progress_callback(f"字幕处理使用极速渲染模式 (硬件编码器: {codec})")
                else:
                    progress_callback(f"字幕处理使用极速渲染模式 (编码器: {codec})")

            # 解析字幕样式设置
            subtitle_style_str = settings.get('subtitle_style', '微软雅黑 22pt 正常 白色 描边黑色2px') if settings else '微软雅黑 22pt 正常 白色 描边黑色2px'
            font_name, font_size, font_weight, font_color, outline_color, outline_width, position = self._parse_subtitle_style_with_position(subtitle_style_str)

            # 构建字幕样式字符串（参考技术文档的方式）
            style_str = f"Alignment={self._convert_position_to_alignment(position)},"

            if font_weight == "粗体":
                style_str += 'Bold=1,'
            else:
                style_str += 'Bold=0,'

            style_str += f"OutlineColour={self._convert_color_to_ffmpeg(outline_color)},"
            style_str += f"BorderStyle=1,Outline={outline_width},Shadow=0,"
            style_str += f"Fontsize={font_size},"
            style_str += f"FontName={font_name},"
            style_str += f"PrimaryColour={self._convert_color_to_ffmpeg(font_color)}"

            if progress_callback:
                progress_callback(f"字幕处理进度: 25% - 应用字幕样式: {font_name} {font_size}pt {font_color}")

            # 转义字幕路径（参考技术文档）
            escaped_subtitle_path = self._escape_path_for_ffmpeg_filter(safe_subtitle_path)

            # 构建FFmpeg命令（参考技术文档的推荐方式）
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-i', video_path,
                '-threads', '0',  # 使用所有可用线程
            ]

            # 使用lavfi方式（技术文档推荐的主要方法）
            cmd.extend([
                '-lavfi', f'subtitles={escaped_subtitle_path}:force_style=\'{style_str}\'',
                '-c:v', subtitle_codec
            ])

            # 添加音频和编码参数
            cmd.extend(['-c:a', 'copy'])
            cmd.extend(subtitle_params)

            # 添加性能优化参数
            cmd.extend([
                '-movflags', '+faststart',  # 快速启动
                '-max_muxing_queue_size', '2048',  # 增加缓冲区
                '-fflags', '+genpts',  # 重新生成时间戳
                '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
                '-vsync', 'cfr'  # 恒定帧率，提高处理速度
            ])

            cmd.append(output_path)

            try:
                # 启动FFmpeg进程（隐藏窗口）
                import platform
                if platform.system() == "Windows":
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                             text=True, encoding='utf-8', errors='ignore',
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                             text=True, encoding='utf-8', errors='ignore')

                # 监控进度（只在非重试时显示详细进度）
                if progress_callback and video_duration and not _is_retry:
                    self._monitor_ffmpeg_progress(progress_file.name, video_duration, progress_callback, "添加字幕")
                elif _is_retry and progress_callback:
                    # 重试时只显示简单状态，不显示百分比进度
                    progress_callback("正在重试字幕添加...")

                # 等待进程完成
                stdout, stderr = process.communicate()

                if process.returncode != 0:
                    # 检查是否是硬件编码器失败
                    is_hardware_encoder = any(hw in codec for hw in ['nvenc', 'qsv', 'amf'])

                    if is_hardware_encoder and use_hardware_for_subtitle:
                        # 硬件编码器失败，尝试软件编码器
                        if progress_callback:
                            progress_callback(f"硬件编码器 {codec} 字幕添加失败，切换到软件编码器...")

                        # 清理当前的临时文件
                        try:
                            os.unlink(progress_file.name)
                        except Exception:
                            pass

                        # 使用软件编码器重试（标记为重试，避免重复进度显示）
                        return self._add_subtitles_with_software_encoder(video_path, safe_subtitle_path, output_path, settings, progress_callback, _is_retry=True)
                    else:
                        # 软件编码器也失败，尝试-vf替代-lavfi（技术文档推荐的备用方案）
                        if progress_callback:
                            progress_callback("lavfi方法失败，尝试-vf替代方案...")

                        # 清理当前的临时文件
                        try:
                            os.unlink(progress_file.name)
                        except Exception:
                            pass

                        # 尝试-vf备用方案
                        return self._add_subtitles_with_vf_fallback(video_path, safe_subtitle_path, output_path, settings, progress_callback, _is_retry=True)

                if progress_callback:
                    progress_callback("字幕添加完成")

            finally:
                # 清理临时进度文件
                try:
                    os.unlink(progress_file.name)
                except Exception:
                    pass

            return True

        finally:
            # 清理临时字幕文件
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass

            # 清理安全路径临时文件
            if 'safe_subtitle_path' in locals():
                try:
                    if os.path.exists(safe_subtitle_path):
                        os.unlink(safe_subtitle_path)
                except Exception:
                    pass

            # 清理临时目录
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass

    def _convert_srt_to_ass(self, srt_path, settings, progress_callback=None):
        """将SRT字幕转换为ASS格式"""
        try:
            if progress_callback:
                progress_callback("字幕处理进度: 15% - 转换SRT为ASS格式")

            # 解析字幕样式
            subtitle_style_str = settings.get('subtitle_style', '微软雅黑 22pt 正常 白色 描边黑色2px') if settings else '微软雅黑 22pt 正常 白色 描边黑色2px'
            font_name, font_size, font_weight, font_color, outline_color, outline_width, position = self._parse_subtitle_style_with_position(subtitle_style_str)

            # 读取SRT内容
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            # 解析SRT内容
            subtitle_entries = self._parse_srt_content(srt_content)

            # 创建临时ASS文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.ass', delete=False, encoding='utf-8') as temp_ass:
                ass_path = temp_ass.name

                # 写入ASS文件头
                temp_ass.write(self._generate_ass_header())

                # 写入样式定义
                temp_ass.write(self._generate_ass_style(font_name, font_size, font_weight, font_color, outline_color, outline_width, position))

                # 写入事件部分
                temp_ass.write("\n[Events]\n")
                temp_ass.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

                # 转换字幕条目
                for entry in subtitle_entries:
                    start_time = self._seconds_to_ass_time(entry['start'])
                    end_time = self._seconds_to_ass_time(entry['end'])
                    text = entry['text'].replace('\n', '\\N')  # ASS换行符

                    # 写入字幕事件
                    temp_ass.write(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}\n")

            if progress_callback:
                progress_callback("字幕处理进度: 20% - ASS格式转换完成")

            return ass_path

        except Exception as e:
            print(f"SRT转ASS失败: {e}")
            return None

    def _generate_ass_header(self):
        """生成ASS文件头"""
        return """[Script Info]
Title: Generated by AI Voice Synthesis
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
"""

    def _generate_ass_style(self, font_name, font_size, font_weight, font_color, outline_color, outline_width, position):
        """生成ASS样式定义"""
        # 颜色转换为ASS格式（BGR格式，十六进制）
        color_map = {
            'white': '&HFFFFFF&', 'black': '&H000000&', 'red': '&H0000FF&',
            'green': '&H00FF00&', 'blue': '&HFF0000&', 'yellow': '&H00FFFF&',
            'cyan': '&HFFFF00&', 'magenta': '&HFF00FF&'
        }

        primary_color = color_map.get(font_color.lower(), '&HFFFFFF&')
        outline_color_hex = color_map.get(outline_color.lower(), '&H000000&')

        # 字体粗细
        bold = 1 if font_weight == "粗体" else 0

        # 对齐方式
        alignment_map = {
            "底部居中": "2", "顶部居中": "8", "左下角": "1", "右下角": "3",
            "左上角": "7", "右上角": "9", "居中": "5"
        }
        alignment = alignment_map.get(position, "2")

        # 边距设置
        margin_v = "20" if position in ["底部居中", "顶部居中"] else "20"
        margin_l = "20" if position in ["左下角", "左上角"] else "0"
        margin_r = "20" if position in ["右下角", "右上角"] else "0"

        # 生成样式行
        style_line = f"Style: Default,{font_name},{font_size},{primary_color},&HFFFFFF&,{outline_color_hex},&H80000000&,{bold},0,0,0,100,100,0,0,1,{outline_width},2,{alignment},{margin_l},{margin_r},{margin_v},1\n"

        return style_line

    def _seconds_to_ass_time(self, seconds):
        """将秒数转换为ASS时间格式 (H:MM:SS.CC)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        centiseconds = int((seconds % 1) * 100)

        return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

    def _parse_subtitle_style_with_position(self, style_str):
        """解析字幕样式字符串，包括位置信息"""
        try:
            # 先使用原有方法解析基本样式
            font_name, font_size, font_weight, font_color, outline_color, outline_width = self._parse_subtitle_style(style_str)

            # 解析位置信息
            position = "底部居中"  # 默认位置
            position_keywords = ["底部居中", "顶部居中", "左下角", "右下角", "左上角", "右上角", "居中"]

            for keyword in position_keywords:
                if keyword in style_str:
                    position = keyword
                    break

            return font_name, font_size, font_weight, font_color, outline_color, outline_width, position

        except Exception as e:
            print(f"解析字幕样式失败: {e}")
            # 返回默认值
            return "微软雅黑", 22, "normal", "white", "black", 2, "底部居中"

    def _parse_subtitle_style(self, style_str):
        """解析字幕样式字符串"""
        try:
            # 默认值
            font_name = "微软雅黑"
            font_size = 22
            font_weight = "normal"
            font_color = "white"
            outline_color = "black"
            outline_width = 2

            # 解析字体名称（第一个词）
            parts = style_str.split()
            if parts:
                font_name = parts[0]

            # 解析字体大小
            for part in parts:
                if 'pt' in part:
                    try:
                        font_size = int(part.replace('pt', ''))
                    except ValueError:
                        pass

            # 解析字体粗细
            if '粗体' in style_str or 'bold' in style_str.lower():
                font_weight = "粗体"

            # 解析描边（先解析描边，避免与字体颜色冲突）
            if '描边' in style_str:
                # 查找描边颜色和宽度
                import re
                outline_match = re.search(r'描边(\w+)(\d+)px', style_str)
                if outline_match:
                    outline_color_cn = outline_match.group(1)
                    outline_width = int(outline_match.group(2))

                    color_map = {
                        '黑色': 'black', '白色': 'white', '红色': 'red', '绿色': 'green',
                        '蓝色': 'blue', '黄色': 'yellow', '青色': 'cyan', '洋红': 'magenta'
                    }
                    outline_color = color_map.get(outline_color_cn, 'black')

            # 解析字体颜色（排除描边部分）
            # 先移除描边部分，避免颜色解析冲突
            style_without_outline = re.sub(r'描边\w+\d+px', '', style_str) if '描边' in style_str else style_str

            colors = ['白色', '黑色', '红色', '绿色', '蓝色', '黄色', '青色', '洋红']
            for color in colors:
                if color in style_without_outline:
                    color_map = {
                        '白色': 'white', '黑色': 'black', '红色': 'red', '绿色': 'green',
                        '蓝色': 'blue', '黄色': 'yellow', '青色': 'cyan', '洋红': 'magenta'
                    }
                    font_color = color_map.get(color, 'white')
                    break

            return font_name, font_size, font_weight, font_color, outline_color, outline_width

        except Exception as e:
            print(f"解析字幕样式失败: {e}")
            return "微软雅黑", 22, "normal", "white", "black", 2

    def _parse_srt_content(self, srt_content):
        """解析SRT字幕内容"""
        entries = []
        blocks = srt_content.strip().split('\n\n')

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 解析时间码
                    time_line = lines[1]
                    start_str, end_str = time_line.split(' --> ')

                    start_time = self._parse_srt_time(start_str)
                    end_time = self._parse_srt_time(end_str)

                    # 合并文本行
                    text = '\n'.join(lines[2:])

                    entries.append({
                        'start': start_time,
                        'end': end_time,
                        'text': text
                    })
                except Exception as e:
                    print(f"解析SRT块失败: {e}")
                    continue

        return entries

    def _parse_srt_time(self, time_str):
        """解析SRT时间格式为秒数"""
        try:
            # SRT格式: HH:MM:SS,mmm
            time_str = time_str.replace(',', '.')
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])

            return hours * 3600 + minutes * 60 + seconds
        except Exception:
            return 0.0

    def compose_video_with_loop(self, loop_video_path, audio_path, output_path,
                               subtitle_path=None, settings=None, progress_callback=None):
        """
        完整的视频合成流程：循环视频 + 音频 + 字幕

        参数:
            loop_video_path (str): 循环视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出视频路径
            subtitle_path (str): 字幕文件路径（可选）
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            if not self.is_available():
                raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")

            self.set_progress_callback(progress_callback)

            if progress_callback:
                progress_callback("开始处理视频...")

            # 创建调试目录
            debug_dir = os.path.join(os.path.dirname(output_path), "debug")
            os.makedirs(debug_dir, exist_ok=True)

            # 获取基础文件名用于调试文件命名
            base_name = os.path.splitext(os.path.basename(output_path))[0]

            # 获取输入文件信息
            try:
                video_duration = self.get_media_duration(loop_video_path)
                audio_duration = self.get_media_duration(audio_path)

                if progress_callback:
                    progress_callback(f"原视频: {video_duration:.2f}秒，音频: {audio_duration:.2f}秒")
                    progress_callback(f"🔍 调试文件将保存到: {debug_dir}")
            except Exception as e:
                raise e

            # 检查是否需要视频编码处理
            need_video_encoding = self._need_video_encoding(settings)

            # 为循环视频设置正确的码率（使用第一集的码率作为默认）
            if settings and settings.get('custom_compression', False):
                if 'current_compression_bitrate' not in settings:
                    settings['current_compression_bitrate'] = settings.get('first_compression_bitrate', 5000)

            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            temp_looped_video = os.path.join(temp_dir, "looped_video.mp4")
            temp_encoded_video = os.path.join(temp_dir, "encoded_video.mp4")
            temp_with_audio = os.path.join(temp_dir, "with_audio.mp4")
            temp_with_subtitle = os.path.join(temp_dir, "with_subtitle.mp4")

            try:
                # 第1步：循环视频到音频时长
                if progress_callback:
                    progress_callback("第1步: 循环视频到音频时长...")

                success = self.loop_video_to_duration(loop_video_path, audio_duration, temp_looped_video, settings, progress_callback)

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success or not os.path.exists(temp_looped_video):
                    raise Exception("循环视频失败")

                # 保存第1步调试文件
                debug_step1 = os.path.join(debug_dir, f"{base_name}_step1_looped.mp4")
                try:
                    import shutil
                    shutil.copy2(temp_looped_video, debug_step1)
                    step1_duration = self.get_media_duration(debug_step1)
                    if progress_callback:
                        progress_callback(f"🔍 第1步完成，循环视频时长: {step1_duration:.2f}秒，已保存到: {os.path.basename(debug_step1)}")
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"⚠️ 保存第1步调试文件失败: {e}")

                current_video = temp_looped_video

                # 第2步：处理分辨率（如果需要且未在循环时处理）
                if settings and settings.get('custom_resolution', False) and not need_video_encoding:
                    width = settings.get('width', 1920)
                    height = settings.get('height', 1080)
                    resolution = f"{width}x{height}"

                    if progress_callback:
                        progress_callback(f"第2步: 调整分辨率到 {resolution}...")

                    temp_resized = os.path.join(temp_dir, "resized.mp4")
                    success = self.resize_video(current_video, resolution, temp_resized, progress_callback)

                    if success == "USER_STOPPED":
                        if progress_callback:
                            progress_callback("视频合成已停止")
                        return False
                    elif not success:
                        raise Exception("分辨率调整失败")

                    # 保存第2步调试文件
                    debug_step2 = os.path.join(debug_dir, f"{base_name}_step2_resized.mp4")
                    try:
                        import shutil
                        shutil.copy2(temp_resized, debug_step2)
                        step2_duration = self.get_media_duration(debug_step2)
                        if progress_callback:
                            progress_callback(f"🔍 第2步完成，分辨率调整后时长: {step2_duration:.2f}秒，已保存到: {os.path.basename(debug_step2)}")
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 保存第2步调试文件失败: {e}")

                    current_video = temp_resized
                else:
                    if progress_callback:
                        if need_video_encoding:
                            progress_callback("第2步: 视频编码已在循环时完成...")
                        else:
                            progress_callback("第2步: 保持原始设置...")

                    # 即使跳过第2步，也保存当前状态
                    debug_step2 = os.path.join(debug_dir, f"{base_name}_step2_unchanged.mp4")
                    try:
                        import shutil
                        shutil.copy2(current_video, debug_step2)
                        step2_duration = self.get_media_duration(debug_step2)
                        if progress_callback:
                            progress_callback(f"🔍 第2步跳过，当前视频时长: {step2_duration:.2f}秒，已保存到: {os.path.basename(debug_step2)}")
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 保存第2步调试文件失败: {e}")

                # 第3步：添加字幕（如果需要）
                if subtitle_path and os.path.exists(subtitle_path) and settings and settings.get('enable_subtitle', False):
                    if progress_callback:
                        progress_callback("第3步: 添加字幕...")

                    self.add_subtitles_ffmpeg(current_video, subtitle_path, temp_with_subtitle, settings, progress_callback)

                    # 保存第3步调试文件
                    debug_step3 = os.path.join(debug_dir, f"{base_name}_step3_with_subtitle.mp4")
                    try:
                        import shutil
                        shutil.copy2(temp_with_subtitle, debug_step3)
                        step3_duration = self.get_media_duration(debug_step3)
                        if progress_callback:
                            progress_callback(f"🔍 第3步完成，添加字幕后时长: {step3_duration:.2f}秒，已保存到: {os.path.basename(debug_step3)}")
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 保存第3步调试文件失败: {e}")

                    current_video = temp_with_subtitle
                else:
                    if progress_callback:
                        progress_callback("第3步: 跳过字幕添加...")

                    # 即使跳过第3步，也保存当前状态
                    debug_step3 = os.path.join(debug_dir, f"{base_name}_step3_no_subtitle.mp4")
                    try:
                        import shutil
                        shutil.copy2(current_video, debug_step3)
                        step3_duration = self.get_media_duration(debug_step3)
                        if progress_callback:
                            progress_callback(f"🔍 第3步跳过，当前视频时长: {step3_duration:.2f}秒，已保存到: {os.path.basename(debug_step3)}")
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 保存第3步调试文件失败: {e}")

                # 第4步：合并音频
                if progress_callback:
                    progress_callback("第4步: 合并音频...")

                success = self.merge_audio_video(current_video, audio_path, temp_with_audio, settings, progress_callback)

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success or not os.path.exists(temp_with_audio):
                    raise Exception("合并音频失败")

                # 保存第4步调试文件
                debug_step4 = os.path.join(debug_dir, f"{base_name}_step4_with_audio.mp4")
                try:
                    import shutil
                    shutil.copy2(temp_with_audio, debug_step4)
                    step4_duration = self.get_media_duration(debug_step4)
                    if progress_callback:
                        progress_callback(f"🔍 第4步完成，合并音频后时长: {step4_duration:.2f}秒，已保存到: {os.path.basename(debug_step4)}")
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"⚠️ 保存第4步调试文件失败: {e}")

                # 第5步：输出最终视频
                if progress_callback:
                    progress_callback("第5步: 输出最终视频...")

                # 检查是否需要最终编码处理
                # 只有在特定情况下才需要编码：
                # 1. 启用了视频编码设置（自定义压缩、分辨率等）
                # 2. 或者在合并模式下需要统一格式
                need_final_encoding = False

                # 如果启用了视频编码设置，需要最终编码
                if need_video_encoding:
                    need_final_encoding = True

                # 检查是否在合并模式下（这种情况可能需要统一格式）
                if settings and settings.get('enable_merge', False):
                    merge_mode = settings.get('merge_mode', 'create_merged')
                    # 只有在"合并原视频与第一集"模式下才可能需要编码
                    if merge_mode == 'merge_with_first':
                        # 这种情况下，如果没有启用视频设置，也不需要编码
                        # 因为后续的合并步骤会处理格式统一
                        pass

                if need_final_encoding:
                    # 需要编码处理
                    success = self._encode_video_with_settings(temp_with_audio, output_path, settings, progress_callback)
                else:
                    # 直接复制，不需要编码
                    success = self._copy_video(temp_with_audio, output_path, progress_callback)

                if not success:
                    raise Exception("输出最终视频失败")

                # 保存最终输出文件的调试信息
                try:
                    final_duration = self.get_media_duration(output_path)
                    if progress_callback:
                        progress_callback(f"🔍 最终视频时长: {final_duration:.2f}秒，目标时长: {audio_duration:.2f}秒")
                        if abs(final_duration - audio_duration) > 5:  # 如果差异超过5秒
                            progress_callback(f"⚠️ 警告：最终视频时长与目标时长差异较大！差异: {abs(final_duration - audio_duration):.2f}秒")
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"⚠️ 获取最终视频时长失败: {e}")

                if progress_callback:
                    progress_callback("✅ 视频合成完成")

                # 清理调试文件（如果不需要保留）
                self._cleanup_debug_files(output_path, settings)

                return True

            finally:
                # 清理临时文件
                try:
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 视频合成失败: {str(e)}")
            print(f"视频合成错误: {e}")
            return False

    def _need_video_encoding(self, settings):
        """检查是否需要视频编码处理"""
        if not settings:
            return False

        # 检查是否有自定义压缩设置
        if settings.get('custom_compression', False):
            return True

        # 检查是否有自定义分辨率设置
        if settings.get('custom_resolution', False):
            return True

        # 可以在这里添加其他需要编码的条件

        return False

    def _encode_video_with_settings(self, input_path, output_path, settings, progress_callback=None):
        """对视频应用所有自定义设置进行编码"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 使用最终输出编码器设置
        video_encoding_settings = settings.copy() if settings else {}
        final_encoder = video_encoding_settings.get('final_encoder', 'auto')
        if final_encoder != 'auto':
            video_encoding_settings['encoder'] = final_encoder

        # 获取编码器设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(video_encoding_settings)

        # 创建临时进度文件
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,
            '-i', input_path,
            '-c:v', codec,
            '-c:a', 'copy'  # 音频直接复制
        ]

        # 添加分辨率设置
        if settings and settings.get('custom_resolution', False):
            width = settings.get('width', 1920)
            height = settings.get('height', 1080)
            cmd.extend(['-vf', f'scale={width}:{height}'])

        # 添加压缩设置（修复跳转问题）
        if settings and settings.get('custom_compression', False):
            compression_bitrate = settings.get('current_compression_bitrate', 5000)
            cmd.extend(['-b:v', f'{compression_bitrate}k'])

            # 为码率模式添加兼容性参数，修复跳转问题
            if 'nvenc' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-rc', 'vbr',
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif 'qsv' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif 'amf' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif codec == 'libx264':
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30',
                    '-profile:v', 'high',
                    '-level', '4.1'
                ])
        else:
            # 使用默认CRF
            cmd.extend(['-crf', '23'])

            # 为CRF模式也添加关键帧设置
            if 'nvenc' in codec or 'qsv' in codec or 'amf' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif codec == 'libx264':
                cmd.extend(['-g', '60', '-keyint_min', '30', '-profile:v', 'high', '-level', '4.1'])

        # 添加编码器优化参数
        cmd.extend(ffmpeg_params)

        # 添加大幅优化的输出格式参数
        output_format = settings.get('format', 'mp4') if settings else 'mp4'
        speed_priority = settings.get('speed_priority', True) if settings else True

        if output_format == 'mp4':
            if speed_priority:
                # 速度优先模式 - 最大化写入性能
                cmd.extend([
                    '-movflags', '+faststart+frag_keyframe+separate_moof+omit_tfhd_offset',
                    '-max_muxing_queue_size', '4096',
                    '-thread_queue_size', '2048',
                    '-flush_packets', '1',
                    '-write_tmcd', '0'  # 禁用时间码写入
                ])
            else:
                cmd.extend(['-movflags', '+faststart'])

        # 添加通用性能优化参数
        if speed_priority:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+flush_packets',
                '-copyts',
                '-start_at_zero'
            ])

        cmd.append(output_path)

        if progress_callback:
            progress_callback(f"视频编码使用编码器: {codec} (最终输出编码器)")

        try:
            # 获取输入视频时长用于进度计算
            try:
                input_duration = self.get_media_duration(input_path)
            except Exception:
                input_duration = None

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file.name, input_duration, progress_callback, "视频编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"视频编码失败: {stderr}")

            return True

        finally:
            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _copy_video(self, input_path, output_path, progress_callback=None):
        """直接复制视频文件"""
        try:
            if progress_callback:
                progress_callback("复制视频文件...")

            import shutil
            shutil.copy2(input_path, output_path)

            if progress_callback:
                progress_callback("视频复制完成")

            return True
        except Exception as e:
            if progress_callback:
                progress_callback(f"视频复制失败: {str(e)}")
            return False

    def _basic_encode(self, input_path, output_path, settings, progress_callback=None):
        """基本编码（无自定义设置时使用）"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 创建临时进度文件
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,
            '-i', input_path,
            '-c:v', 'libx264',  # 使用软件编码器
            '-crf', '23',       # 默认质量
            '-c:a', 'aac',      # 音频编码
            '-movflags', '+faststart',
            output_path
        ]

        if progress_callback:
            progress_callback("进行基本编码...")

        try:
            # 获取输入视频时长用于进度计算
            try:
                input_duration = self.get_media_duration(input_path)
            except Exception:
                input_duration = None

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file.name, input_duration, progress_callback, "基本编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"基本编码失败: {stderr}")

            return True

        finally:
            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _final_encode(self, input_path, output_path, settings, progress_callback=None):
        """视频编码输出，支持编码器自动回退"""
        return self._final_encode_with_fallback(input_path, output_path, settings, progress_callback)

    def _final_encode_with_fallback(self, input_path, output_path, settings, progress_callback=None, retry_count=0):
        """视频编码输出，支持编码器回退机制"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 检查是否启用自定义压缩
        use_custom_compression = settings and settings.get('custom_compression', False)

        if use_custom_compression:
            # 获取码率设置
            compression_bitrate = settings.get('current_compression_bitrate', 5000)

            # 直接使用码率而不是CRF
            use_bitrate = True
            bitrate_value = f"{compression_bitrate}k"
            crf = None
        else:
            # 不使用自定义压缩，使用默认的CRF设置
            use_bitrate = False
            crf = '23'  # 默认中等质量
            compression_bitrate = None  # 不显示码率

        # 获取最终输出专用编码器设置
        final_settings = settings.copy() if settings else {}
        final_encoder = final_settings.get('final_encoder', 'auto')
        if final_encoder != 'auto':
            final_settings['encoder'] = final_encoder

        # 获取编码设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(final_settings)

        if progress_callback:
            progress_callback(f"最终输出使用编码器: {codec}")

        # 获取输出格式
        output_format = settings.get('format', 'mp4') if settings else 'mp4'

        # 获取输入视频时长
        try:
            input_duration = self.get_media_duration(input_path)
        except Exception:
            input_duration = None

        # 创建临时进度文件
        progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
        progress_file.close()

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file.name,  # 输出进度到文件
            '-i', input_path,
            '-c:v', codec,
            '-c:a', 'aac'
        ]

        # 根据压缩设置添加质量参数
        if use_bitrate:
            cmd.extend(['-b:v', bitrate_value])

            # 为码率模式添加兼容性参数，修复跳转问题
            if 'nvenc' in codec:
                # NVIDIA编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",  # 最大码率为目标码率的1.5倍
                    '-bufsize', f"{compression_bitrate * 2}k",         # 缓冲区大小
                    '-rc', 'vbr',                                      # 使用VBR而不是CBR
                    '-g', '60',                                        # 关键帧间隔2秒（30fps）
                    '-keyint_min', '30',                               # 最小关键帧间隔1秒
                    '-bf', '3',                                        # B帧数量
                    '-refs', '3'                                       # 参考帧数量
                ])
            elif 'qsv' in codec:
                # Intel编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30',
                    '-bf', '3'
                ])
            elif 'amf' in codec:
                # AMD编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif codec == 'libx264':
                # 软件编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',                                        # 关键帧间隔
                    '-keyint_min', '30',                               # 最小关键帧间隔
                    '-bf', '3',                                        # B帧数量
                    '-refs', '3',                                      # 参考帧数量
                    '-profile:v', 'high',                              # 使用high profile
                    '-level', '4.1'                                    # 兼容性级别
                ])
        else:
            cmd.extend(['-crf', crf])

            # 为CRF模式也添加兼容性参数
            if 'nvenc' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif 'qsv' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif 'amf' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif codec == 'libx264':
                cmd.extend(['-g', '60', '-keyint_min', '30', '-profile:v', 'high', '-level', '4.1'])

        # 添加优化参数（在兼容性参数之后）
        cmd.extend(ffmpeg_params)

        # 根据输出格式添加特定参数
        if output_format == 'mp4':
            cmd.extend(['-movflags', '+faststart'])
        elif output_format == 'mkv':
            cmd.extend(['-f', 'matroska'])
        elif output_format == 'avi':
            cmd.extend(['-f', 'avi'])
        elif output_format == 'mov':
            cmd.extend(['-f', 'mov'])

        cmd.append(output_path)

        if progress_callback:
            if use_custom_compression and compression_bitrate is not None:
                progress_callback(f"使用编码器: {codec}, 码率: {bitrate_value}")
            else:
                progress_callback(f"使用编码器: {codec}, CRF: {crf} (保持原始质量)")

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file.name, input_duration, progress_callback, "视频编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是硬件编码器失败
                is_hardware_encoder = any(hw in codec for hw in ['nvenc', 'qsv', 'amf'])

                if is_hardware_encoder and retry_count == 0:
                    # 硬件编码器失败，尝试软件编码器
                    if progress_callback:
                        progress_callback(f"硬件编码器 {codec} 失败，切换到软件编码器...")

                    # 清理当前的临时文件
                    try:
                        os.unlink(progress_file.name)
                    except Exception:
                        pass

                    # 强制使用软件编码器重试
                    fallback_settings = settings.copy() if settings else {}
                    fallback_settings['encoder'] = 'libx264'  # 强制使用软件编码器
                    fallback_settings['use_hardware'] = False

                    return self._final_encode_with_fallback(input_path, output_path, fallback_settings, progress_callback, retry_count + 1)
                else:
                    # 软件编码器也失败，或者已经重试过
                    raise Exception(f"视频编码失败: {stderr}")

        finally:
            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _get_optimal_encoder_settings(self, settings):
        """获取最优编码器设置 - 全面优化版本"""
        # 获取用户设置
        use_hardware = settings.get('use_hardware_acceleration', True)
        encoder_choice = settings.get('encoder', 'auto')
        encoding_speed = settings.get('encoding_speed', 'fast')
        fast_mode = settings.get('fast_mode', True)  # 默认启用快速模式
        speed_priority = settings.get('speed_priority', True)  # 新增：速度优先模式
        enable_parallel = settings.get('enable_parallel', True)
        max_threads = settings.get('max_threads', 8)  # 增加默认线程数

        # 如果用户指定了编码器
        if encoder_choice != 'auto':
            if self._check_encoder_available(encoder_choice):
                codec = encoder_choice
                # 根据编码器类型设置优化参数
                if 'nvenc' in encoder_choice:
                    # NVIDIA编码器 - 修复跳转问题
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-preset', 'p2',  # 改为p2，p1太激进可能影响跳转
                            '-tune', 'hq',    # 改为hq，提高质量和兼容性
                            '-rc', 'vbr',     # 改为vbr，提高兼容性
                            '-cq', '23',      # 添加质量控制
                            '-g', '30',       # 强制关键帧间隔30帧（1秒）
                            '-keyint_min', '30',  # 最小关键帧间隔
                            '-forced-idr', '1',   # 强制IDR帧
                            '-spatial_aq', '1',
                            '-temporal_aq', '1',
                            '-gpu', '0'       # 指定GPU
                        ]
                    else:
                        ffmpeg_params = [
                            '-preset', 'p4',  # 平衡质量和速度
                            '-profile:v', 'high',
                            '-rc', 'vbr',
                            '-cq', '23',      # 优化质量
                            '-g', '30',       # 关键帧间隔
                            '-keyint_min', '30',
                            '-spatial_aq', '1',
                            '-temporal_aq', '1',
                            '-rc-lookahead', '20'
                        ]
                elif 'qsv' in encoder_choice:
                    # Intel编码器 - 修复跳转问题
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-preset', 'fast',  # 改为fast，提高兼容性
                            '-g', '30',         # 关键帧间隔
                            '-keyint_min', '30',
                            '-async_depth', '4',
                            '-look_ahead', '0'
                        ]
                    else:
                        ffmpeg_params = [
                            '-preset', 'medium',
                            '-profile:v', 'high',
                            '-global_quality', '22',
                            '-g', '30',         # 关键帧间隔
                            '-keyint_min', '30',
                            '-look_ahead', '1',
                            '-la_depth', '20',
                            '-mbbrc', '1'
                        ]
                elif 'amf' in encoder_choice:
                    # AMD编码器 - 修复跳转问题
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-quality', 'balanced',  # 改为balanced，提高兼容性
                            '-usage', 'transcoding', # 改为transcoding
                            '-rc', 'vbr',           # 改为vbr
                            '-g', '30',             # 关键帧间隔
                            '-keyint_min', '30'
                        ]
                    else:
                        ffmpeg_params = [
                            '-quality', 'quality',  # 最高质量
                            '-profile:v', 'high',
                            '-usage', 'transcoding',
                            '-rc', 'vbr',
                            '-g', '30',             # 关键帧间隔
                            '-keyint_min', '30'
                        ]
                else:
                    # 软件编码器 - 大幅优化
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-preset', 'superfast',  # 比ultrafast质量更好但仍很快
                            '-tune', 'zerolatency',
                            '-x264-params', 'ref=1:bframes=0:subme=1:me_range=4:rc_lookahead=0:trellis=0:8x8dct=0'
                        ]
                    else:
                        ffmpeg_params = [
                            '-preset', 'fast',
                            '-tune', 'film',
                            '-x264-params', 'ref=3:bframes=3:subme=6:me_range=16'
                        ]

                    # 软件编码器添加优化的线程参数
                    if enable_parallel and max_threads > 0:
                        ffmpeg_params.extend([
                            '-threads', str(min(max_threads, 16)),  # 限制最大线程数
                            '-thread_type', 'frame+slice',
                            '-slices', str(min(max_threads, 8))
                        ])

                return codec, ffmpeg_params
            else:
                print(f"指定的编码器 {encoder_choice} 不可用，回退到自动选择")
                # 如果是硬件编码器失败，强制使用软件编码器
                if any(hw in encoder_choice for hw in ['nvenc', 'qsv', 'amf']):
                    use_hardware = False

        # 自动选择编码器 - 优化选择策略
        if use_hardware:
            # 尝试NVIDIA编码器（最稳定的硬件编码器）
            if self._check_encoder_available('h264_nvenc'):
                try:
                    if speed_priority or fast_mode:
                        return 'h264_nvenc', [
                            '-preset', 'p1', '-tune', 'ull', '-rc', 'cbr',
                            '-surfaces', '64', '-forced-idr', '1'
                        ]
                    else:
                        return 'h264_nvenc', [
                            '-preset', 'p2', '-profile:v', 'high', '-rc', 'vbr',
                            '-cq', '25', '-spatial_aq', '1', '-temporal_aq', '1'
                        ]
                except Exception:
                    pass

            # Intel编码器作为备选
            if self._check_encoder_available('h264_qsv'):
                try:
                    if speed_priority or fast_mode:
                        return 'h264_qsv', [
                            '-preset', 'veryfast', '-async_depth', '4', '-look_ahead', '0'
                        ]
                    else:
                        return 'h264_qsv', [
                            '-preset', 'fast', '-profile:v', 'high', '-global_quality', '22',
                            '-look_ahead', '1', '-mbbrc', '1'
                        ]
                except Exception:
                    pass

            # AMD编码器最后尝试
            if self._check_encoder_available('h264_amf'):
                try:
                    if speed_priority or fast_mode:
                        return 'h264_amf', ['-quality', 'speed', '-usage', 'lowlatency']
                    else:
                        return 'h264_amf', ['-quality', 'balanced', '-usage', 'transcoding']
                except Exception:
                    pass

        # 回退到优化的软件编码
        codec = 'libx264'
        if speed_priority or fast_mode:
            ffmpeg_params = [
                '-preset', 'fast',      # 改为fast，提高兼容性
                '-tune', 'film',        # 改为film，提高质量
                '-g', '30',             # 关键帧间隔30帧
                '-keyint_min', '30',    # 最小关键帧间隔
                '-x264-params', 'ref=2:bframes=1:subme=4:me_range=8'  # 平衡参数
            ]
        else:
            ffmpeg_params = [
                '-preset', 'medium',    # 更好的质量
                '-tune', 'film',
                '-g', '30',             # 关键帧间隔
                '-keyint_min', '30',
                '-x264-params', 'ref=4:bframes=3:subme=7'
            ]

        # 软件编码器添加优化的并行参数
        if enable_parallel and max_threads > 0:
            ffmpeg_params.extend([
                '-threads', str(min(max_threads, 16)),
                '-thread_type', 'frame+slice',
                '-slices', str(min(max_threads, 8))
            ])

        return codec, ffmpeg_params

    def _analyze_video_complexity(self, video_path):
        """分析视频复杂度以优化编码参数"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return 'medium'

            # 使用ffprobe分析视频特征
            cmd = [
                ffmpeg_exe, '-i', video_path,
                '-vf', 'select=eq(n\\,0)',  # 选择第一帧
                '-f', 'null', '-'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  timeout=10, encoding='utf-8', errors='ignore')

            # 简单的复杂度判断（基于文件大小和时长）
            try:
                file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
                duration = self.get_media_duration(video_path)

                if duration > 0:
                    bitrate_estimate = (file_size * 8) / duration  # Mbps估算

                    if bitrate_estimate > 5:
                        return 'high'
                    elif bitrate_estimate > 2:
                        return 'medium'
                    else:
                        return 'low'

            except Exception:
                pass

            return 'medium'

        except Exception:
            return 'medium'

    def _get_smart_compression_settings(self, settings, video_path=None):
        """智能压缩设置 - 根据内容和用户偏好动态调整"""
        speed_priority = settings.get('speed_priority', True)

        # 基础设置
        if speed_priority:
            # 速度优先 - 使用更激进的压缩
            base_settings = {
                'crf_high_complexity': 28,
                'crf_medium_complexity': 26,
                'crf_low_complexity': 24,
                'preset': 'superfast'
            }
        else:
            # 质量优先 - 更保守的压缩
            base_settings = {
                'crf_high_complexity': 25,
                'crf_medium_complexity': 23,
                'crf_low_complexity': 21,
                'preset': 'fast'
            }

        # 如果提供了视频路径，分析复杂度
        if video_path and os.path.exists(video_path):
            complexity = self._analyze_video_complexity(video_path)

            if complexity == 'high':
                return base_settings['crf_high_complexity'], base_settings['preset']
            elif complexity == 'low':
                return base_settings['crf_low_complexity'], base_settings['preset']
            else:
                return base_settings['crf_medium_complexity'], base_settings['preset']

        # 默认中等复杂度
        return base_settings['crf_medium_complexity'], base_settings['preset']

    def run_performance_benchmark(self, progress_callback=None):
        """运行性能基准测试，提供优化建议"""
        try:
            if progress_callback:
                progress_callback("开始性能基准测试...")

            # 创建测试视频（5秒，简单内容）
            test_video = self._create_test_video()
            if not test_video:
                return None

            results = {}

            # 测试不同编码器的性能
            encoders_to_test = []

            # 检测可用的硬件编码器
            if self._check_encoder_available('h264_nvenc'):
                encoders_to_test.append(('h264_nvenc', 'NVIDIA NVENC'))
            if self._check_encoder_available('h264_qsv'):
                encoders_to_test.append(('h264_qsv', 'Intel QSV'))
            if self._check_encoder_available('h264_amf'):
                encoders_to_test.append(('h264_amf', 'AMD AMF'))

            # 总是测试软件编码器
            encoders_to_test.append(('libx264', '软件编码'))

            for i, (encoder, name) in enumerate(encoders_to_test):
                if progress_callback:
                    progress_callback(f"测试 {name} 编码器... ({i+1}/{len(encoders_to_test)})")

                # 测试编码速度
                start_time = time.time()
                success = self._test_encoder_performance(test_video, encoder)
                end_time = time.time()

                if success:
                    encode_time = end_time - start_time
                    results[encoder] = {
                        'name': name,
                        'time': encode_time,
                        'fps': 5.0 / encode_time if encode_time > 0 else 0,
                        'available': True
                    }
                else:
                    results[encoder] = {
                        'name': name,
                        'time': float('inf'),
                        'fps': 0,
                        'available': False
                    }

            # 清理测试文件
            try:
                os.unlink(test_video)
            except:
                pass

            if progress_callback:
                progress_callback("性能测试完成")

            return self._generate_performance_recommendations(results)

        except Exception as e:
            if progress_callback:
                progress_callback(f"性能测试失败: {str(e)}")
            return None

    def _create_test_video(self):
        """创建用于性能测试的简单视频"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 创建临时测试视频文件
            test_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name

            # 生成5秒的测试视频（简单的彩色条纹）
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=5:size=1280x720:rate=30',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-crf', '30',
                test_video
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=30)

            if result.returncode == 0 and os.path.exists(test_video):
                return test_video
            else:
                return None

        except Exception:
            return None

    def _test_encoder_performance(self, input_video, encoder):
        """测试特定编码器的性能"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            output_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name

            # 构建测试命令
            cmd = [
                ffmpeg_exe, '-y',
                '-i', input_video,
                '-c:v', encoder,
                '-t', '5',  # 只编码5秒
                '-f', 'null',  # 不实际写入文件，只测试编码速度
                '-'
            ]

            # 添加编码器特定参数
            if 'nvenc' in encoder:
                cmd.insert(-2, '-preset')
                cmd.insert(-2, 'fast')
            elif 'qsv' in encoder:
                cmd.insert(-2, '-preset')
                cmd.insert(-2, 'fast')
            elif 'amf' in encoder:
                cmd.insert(-2, '-quality')
                cmd.insert(-2, 'speed')
            elif 'libx264' in encoder:
                cmd.insert(-2, '-preset')
                cmd.insert(-2, 'superfast')

            result = subprocess.run(cmd, capture_output=True, timeout=30)

            # 清理临时文件
            try:
                os.unlink(output_video)
            except:
                pass

            return result.returncode == 0

        except Exception:
            return False

    def _generate_performance_recommendations(self, results):
        """根据测试结果生成性能建议"""
        recommendations = {
            'fastest_encoder': None,
            'recommended_settings': {},
            'performance_summary': [],
            'optimization_tips': []
        }

        # 找出最快的可用编码器
        available_results = {k: v for k, v in results.items() if v['available']}

        if available_results:
            fastest = min(available_results.items(), key=lambda x: x[1]['time'])
            recommendations['fastest_encoder'] = {
                'encoder': fastest[0],
                'name': fastest[1]['name'],
                'time': fastest[1]['time'],
                'fps': fastest[1]['fps']
            }

        # 生成性能摘要
        for encoder, data in results.items():
            if data['available']:
                recommendations['performance_summary'].append(
                    f"{data['name']}: {data['time']:.2f}秒 ({data['fps']:.1f} FPS)"
                )
            else:
                recommendations['performance_summary'].append(
                    f"{data['name']}: 不可用"
                )

        # 生成优化建议
        if recommendations['fastest_encoder']:
            fastest_name = recommendations['fastest_encoder']['name']
            recommendations['optimization_tips'].append(
                f"建议使用 {fastest_name} 以获得最佳性能"
            )

        # 硬件加速建议
        hw_encoders = [k for k, v in results.items() if 'nvenc' in k or 'qsv' in k or 'amf' in k]
        available_hw = [k for k in hw_encoders if results[k]['available']]

        if not available_hw:
            recommendations['optimization_tips'].append(
                "未检测到硬件编码器，建议检查GPU驱动或使用支持硬件加速的GPU"
            )

        # 线程数建议
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        if cpu_count >= 8:
            recommendations['optimization_tips'].append(
                f"检测到 {cpu_count} 核CPU，建议设置最大线程数为 {min(cpu_count, 16)}"
            )

        return recommendations

    def _check_encoder_available(self, encoder):
        """检查编码器是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 首先检查编码器是否在列表中
            cmd = [ffmpeg_exe, '-encoders']
            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True)

            if encoder not in result.stdout:
                return False

            # 对于硬件编码器，进行实际测试
            if any(hw in encoder for hw in ['nvenc', 'qsv', 'amf']):
                return self._test_hardware_encoder(encoder)

            return True
        except Exception:
            return False

    def _test_hardware_encoder(self, encoder):
        """实际测试硬件编码器是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 创建一个简单的测试视频来验证编码器
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                temp_output = temp_file.name

            try:
                # 使用testsrc生成1秒的测试视频
                cmd = [
                    ffmpeg_exe, '-y',
                    '-f', 'lavfi',
                    '-i', 'testsrc=duration=1:size=320x240:rate=1',
                    '-c:v', encoder,
                    '-t', '1',
                    '-f', 'mp4',
                    temp_output
                ]

                import platform
                if platform.system() == "Windows":
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                success = result.returncode == 0

                # 清理测试文件
                try:
                    import os
                    os.unlink(temp_output)
                except Exception:
                    pass

                return success

            except Exception:
                # 清理测试文件
                try:
                    import os
                    os.unlink(temp_output)
                except Exception:
                    pass
                return False

        except Exception:
            return False

    def _detect_hardware_encoders(self):
        """检测可用的硬件编码器"""
        hardware_encoders = []
        test_encoders = ['h264_nvenc', 'h264_qsv', 'h264_amf']

        for encoder in test_encoders:
            if self._check_encoder_available(encoder):
                hardware_encoders.append(encoder)

        return hardware_encoders

    def test_encoder_performance(self, test_video_path=None, progress_callback=None):
        """
        测试不同编码器的性能

        参数:
            test_video_path (str): 测试视频路径，如果为None则创建临时测试视频
            progress_callback (function): 进度回调函数

        返回:
            dict: 编码器性能测试结果
        """
        import time
        import tempfile
        import os

        if progress_callback:
            progress_callback("开始编码器性能测试...")

        # 准备测试视频
        if test_video_path is None or not os.path.exists(test_video_path):
            # 创建临时测试视频
            test_video_path = self._create_test_video(progress_callback)
            if not test_video_path:
                return {"error": "无法创建测试视频"}
            cleanup_test_video = True
        else:
            cleanup_test_video = False

        # 测试的编码器列表
        test_encoders = [
            ('libx264', '软件编码器 (x264)'),
            ('h264_nvenc', 'NVIDIA硬件编码器'),
            ('h264_qsv', 'Intel硬件编码器'),
            ('h264_amf', 'AMD硬件编码器')
        ]

        results = {}
        temp_dir = tempfile.mkdtemp()

        try:
            for i, (encoder, display_name) in enumerate(test_encoders):
                if progress_callback:
                    progress_callback(f"测试 {display_name}... ({i+1}/{len(test_encoders)})")

                # 检查编码器是否可用
                if not self._check_encoder_available(encoder):
                    results[encoder] = {
                        'name': display_name,
                        'available': False,
                        'time': None,
                        'error': '编码器不可用'
                    }
                    continue

                # 测试编码器性能
                output_path = os.path.join(temp_dir, f"test_{encoder}.mp4")
                start_time = time.time()

                try:
                    success = self._test_single_encoder(test_video_path, output_path, encoder)
                    end_time = time.time()

                    if success:
                        encode_time = end_time - start_time
                        results[encoder] = {
                            'name': display_name,
                            'available': True,
                            'time': encode_time,
                            'error': None
                        }
                        if progress_callback:
                            progress_callback(f"✓ {display_name}: {encode_time:.2f}秒")
                    else:
                        results[encoder] = {
                            'name': display_name,
                            'available': True,
                            'time': None,
                            'error': '编码失败'
                        }
                        if progress_callback:
                            progress_callback(f"✗ {display_name}: 编码失败")

                except Exception as e:
                    results[encoder] = {
                        'name': display_name,
                        'available': True,
                        'time': None,
                        'error': str(e)
                    }
                    if progress_callback:
                        progress_callback(f"✗ {display_name}: {str(e)}")

                # 清理临时输出文件
                try:
                    if os.path.exists(output_path):
                        os.unlink(output_path)
                except Exception:
                    pass

        finally:
            # 清理临时目录
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except Exception:
                pass

            # 清理测试视频
            if cleanup_test_video and test_video_path:
                try:
                    os.unlink(test_video_path)
                except Exception:
                    pass

        if progress_callback:
            progress_callback("编码器性能测试完成")

        return results

    def _create_test_video(self, progress_callback=None):
        """创建用于测试的临时视频"""
        import tempfile
        import os

        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return None

        if progress_callback:
            progress_callback("创建测试视频...")

        # 创建临时文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video.close()

        try:
            # 创建10秒的测试视频（纯色+噪声）
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=10:size=1280x720:rate=30',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-crf', '23',
                '-t', '10',
                temp_video.name
            ]

            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True)

            if result.returncode == 0:
                return temp_video.name
            else:
                # 清理失败的文件
                try:
                    os.unlink(temp_video.name)
                except Exception:
                    pass
                return None

        except Exception:
            # 清理失败的文件
            try:
                os.unlink(temp_video.name)
            except Exception:
                pass
            return None

    def _test_single_encoder(self, input_path, output_path, encoder):
        """测试单个编码器的性能"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 构建测试命令（编码5秒视频）
            cmd = [
                ffmpeg_exe, '-y',
                '-i', input_path,
                '-c:v', encoder,
                '-t', '5',  # 只编码5秒以加快测试
                '-an',  # 不处理音频
                output_path
            ]

            # 根据编码器类型添加特定参数
            if encoder == 'libx264':
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'nvenc' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'qsv' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'amf' in encoder:
                cmd.insert(-1, '-quality')
                cmd.insert(-1, 'speed')

            # 隐藏FFmpeg窗口并设置超时
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, timeout=30,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, timeout=30)

            return result.returncode == 0

        except subprocess.TimeoutExpired:
            return False
        except Exception:
            return False





    def _detect_audio_codec(self, audio_path):
        """检测音频文件的编码格式"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 构建ffprobe路径
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
            if not os.path.exists(ffprobe_exe):
                # 尝试其他可能的路径
                ffmpeg_dir = os.path.dirname(ffmpeg_exe)
                ffprobe_exe = os.path.join(ffmpeg_dir, 'ffprobe.exe')
                if not os.path.exists(ffprobe_exe):
                    # 使用ffmpeg进行检测
                    return self._detect_audio_codec_with_ffmpeg(audio_path)

            # 使用ffprobe检测音频编码格式
            cmd = [
                ffprobe_exe, '-v', 'quiet',
                '-select_streams', 'a:0',
                '-show_entries', 'stream=codec_name',
                '-of', 'csv=p=0',
                audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                codec = result.stdout.strip()
                return codec

        except Exception as e:
            print(f"音频格式检测失败: {e}")

        return None

    def _detect_audio_codec_with_ffmpeg(self, audio_path):
        """使用ffmpeg检测音频编码格式（备用方法）"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 使用ffmpeg -i 获取文件信息
            cmd = [ffmpeg_exe, '-i', audio_path, '-f', 'null', '-']

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

            # 解析stderr中的音频信息
            if result.stderr:
                lines = result.stderr.split('\n')
                for line in lines:
                    if 'Audio:' in line:
                        # 提取编码格式，例如: "Audio: aac (LC) (mp4a / 0x6134706D)"
                        parts = line.split('Audio:')[1].strip().split()
                        if parts:
                            codec = parts[0].split('(')[0].strip()
                            return codec

        except Exception as e:
            print(f"FFmpeg音频格式检测失败: {e}")

        return None

    def _test_video_playability(self, video_path):
        """测试视频是否可以正常播放"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 使用ffprobe检查视频完整性
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
            if not os.path.exists(ffprobe_exe):
                ffprobe_exe = os.path.join(os.path.dirname(ffmpeg_exe), 'ffprobe.exe')

            if os.path.exists(ffprobe_exe):
                cmd = [
                    ffprobe_exe, '-v', 'error',
                    '-select_streams', 'v:0',
                    '-show_entries', 'stream=codec_name,width,height,duration',
                    '-of', 'csv=p=0',
                    video_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)
                return result.returncode == 0 and result.stdout.strip()
            else:
                # 备用方法：尝试读取视频帧
                cmd = [
                    ffmpeg_exe, '-v', 'error',
                    '-i', video_path,
                    '-t', '1',
                    '-f', 'null', '-'
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)
                return result.returncode == 0

        except Exception:
            return False

    def _monitor_ffmpeg_progress(self, progress_file, total_duration, progress_callback, operation_name):
        """监控FFmpeg进度"""
        import threading
        import time

        def monitor():
            last_time = 0
            last_percent = 0
            start_time = time.time()

            # 重置时间估算历史
            if hasattr(self, '_time_estimates'):
                delattr(self, '_time_estimates')

            while True:
                try:
                    if os.path.exists(progress_file):
                        with open(progress_file, 'r') as f:
                            content = f.read()

                        # 解析进度信息
                        lines = content.strip().split('\n')
                        current_time = 0
                        speed = None

                        for line in lines:
                            if line.startswith('out_time_ms='):
                                try:
                                    time_ms = int(line.split('=')[1])
                                    current_time = time_ms / 1000000.0  # 转换为秒
                                except ValueError:
                                    continue
                            elif line.startswith('out_time='):
                                time_str = line.split('=')[1]
                                current_time = self._parse_time_string(time_str)
                            elif line.startswith('speed='):
                                speed_str = line.split('=')[1].replace('x', '')
                                try:
                                    speed = float(speed_str)
                                except ValueError:
                                    speed = None

                        # 计算进度百分比
                        if current_time > last_time and total_duration > 0:
                            progress_percent = min(100, (current_time / total_duration) * 100)

                            # 只在进度有明显变化时更新（避免过于频繁的更新）
                            if progress_percent - last_percent >= 1.0 or progress_percent >= 99.5:
                                elapsed_time = time.time() - start_time

                                # 构建进度消息
                                progress_msg = f"{operation_name}: {progress_percent:.1f}%"

                                # 添加时间信息
                                if speed and speed > 0:
                                    progress_msg += f" (速度: {speed:.1f}x)"

                                # 添加预计剩余时间（改进的预测算法）
                                if progress_percent > 10 and elapsed_time > 5:  # 至少处理10%且超过5秒才估算
                                    # 使用加权平均来平滑预测，减少波动
                                    if not hasattr(self, '_time_estimates'):
                                        self._time_estimates = []

                                    # 计算当前的时间估算
                                    current_estimate = elapsed_time / (progress_percent / 100)
                                    self._time_estimates.append(current_estimate)

                                    # 只保留最近的5个估算值
                                    if len(self._time_estimates) > 5:
                                        self._time_estimates.pop(0)

                                    # 使用加权平均（最近的估算权重更高）
                                    weights = [1, 1.2, 1.5, 1.8, 2.0][-len(self._time_estimates):]
                                    weighted_sum = sum(est * weight for est, weight in zip(self._time_estimates, weights))
                                    weight_sum = sum(weights)
                                    estimated_total = weighted_sum / weight_sum

                                    remaining_time = estimated_total - elapsed_time

                                    # 对于接近完成的任务，减少预测时间（因为通常会加速）
                                    if progress_percent > 80:
                                        remaining_time *= 0.7  # 减少30%
                                    elif progress_percent > 60:
                                        remaining_time *= 0.85  # 减少15%

                                    if remaining_time > 0:
                                        remaining_min = int(remaining_time // 60)
                                        remaining_sec = int(remaining_time % 60)
                                        progress_msg += f" (预计剩余: {remaining_min:02d}:{remaining_sec:02d})"

                                progress_callback(progress_msg)
                                last_percent = progress_percent

                            last_time = current_time

                            # 如果接近完成，退出监控
                            if progress_percent >= 99.8:
                                break

                    time.sleep(0.3)  # 每0.3秒检查一次

                except Exception as e:
                    print(f"进度监控错误: {e}")
                    break

        # 在后台线程中监控
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

        # 等待监控线程完成（确保进度更新完整）
        try:
            monitor_thread.join(timeout=1)  # 最多等待1秒
        except Exception:
            pass

    def _parse_time_string(self, time_str):
        """解析时间字符串为秒数"""
        try:
            # 格式: HH:MM:SS.mmm
            parts = time_str.split(':')
            if len(parts) == 3:
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600 + minutes * 60 + seconds
        except Exception:
            pass
        return 0

    def compose_video_with_audio(self, video_path, audio_path, output_path,
                               subtitle_path=None, settings=None, progress_callback=None):
        """
        将音频与视频合成，可选添加字幕（不循环视频）

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出视频路径
            subtitle_path (str): 字幕文件路径（可选）
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            if not self.is_available():
                raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")

            if progress_callback:
                progress_callback("开始合成视频...")

            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            temp_with_audio = os.path.join(temp_dir, "with_audio.mp4")
            temp_with_subtitle = os.path.join(temp_dir, "with_subtitle.mp4")

            try:
                # 第1步：添加字幕（如果需要）
                current_video = video_path
                if subtitle_path and os.path.exists(subtitle_path) and settings and settings.get('enable_subtitle', False):
                    if progress_callback:
                        progress_callback("第1步: 添加字幕...")
                    self.add_subtitles_ffmpeg(current_video, subtitle_path, temp_with_subtitle, settings, progress_callback)
                    current_video = temp_with_subtitle

                # 第2步：合并音视频
                if progress_callback:
                    progress_callback("第2步: 合并音视频...")
                success = self.merge_audio_video(current_video, audio_path, temp_with_audio, settings, progress_callback)

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success:
                    raise Exception("合并音视频失败")

                # 第3步：视频编码
                self._final_encode(temp_with_audio, output_path, settings, progress_callback)

                if progress_callback:
                    progress_callback("✅ 视频合成完成")

                # 清理调试文件（如果不需要保留）
                self._cleanup_debug_files(output_path, settings)

                return True

            finally:
                # 清理临时文件
                import shutil
                try:
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 视频合成失败: {str(e)}")
            print(f"视频合成错误: {e}")
            return False

    def _add_subtitles_with_software_encoder(self, video_path, subtitle_path, output_path, settings=None, progress_callback=None, _is_retry=False):
        """使用软件编码器添加字幕（硬件编码器失败时的重试方案）"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 强制使用软件编码器
        software_settings = settings.copy() if settings else {}
        software_settings['use_hardware_acceleration'] = False
        software_settings['encoder_choice'] = 'libx264'  # 明确指定软件编码器

        codec, ffmpeg_params = self._get_optimal_encoder_settings(software_settings)

        if progress_callback:
            progress_callback(f"使用软件编码器重试添加字幕... (编码器: {codec})")

        try:
            # 获取视频时长用于进度计算
            try:
                video_duration = self.get_media_duration(video_path)
            except Exception:
                video_duration = None

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 构建软件编码命令
            subtitle_filter_path = subtitle_path.replace('\\', '/').replace(':', '\\:')
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-i', video_path,
                '-vf', f'subtitles=filename=\'{subtitle_filter_path}\'',
                '-c:v', codec,
                '-c:a', 'copy'
            ]

            # 添加软件编码器参数
            cmd.extend(ffmpeg_params)
            cmd.append(output_path)

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度（重试时不显示详细进度）
            if progress_callback and video_duration and not _is_retry:
                self._monitor_ffmpeg_progress(progress_file.name, video_duration, progress_callback, "软件编码添加字幕")
            elif _is_retry and progress_callback:
                progress_callback("软件编码器重试中...")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 软件编码器也失败，使用最简备用方案
                if progress_callback:
                    progress_callback("软件编码器也失败，使用最简备用方案...")

                return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

            if progress_callback:
                progress_callback("软件编码器字幕添加完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"软件编码器字幕添加失败: {str(e)}")

            # 最后尝试备用方案
            return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

        finally:
            # 清理临时文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _add_subtitles_with_vf_fallback(self, video_path, subtitle_path, output_path, settings, progress_callback=None, _is_retry=False):
        """使用-vf替代-lavfi的备用方案（技术文档推荐）"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        if progress_callback and not _is_retry:
            progress_callback("使用-vf备用方案添加字幕...")

        try:
            # 解析字幕样式设置
            subtitle_style_str = settings.get('subtitle_style', '微软雅黑 22pt 正常 白色 描边黑色2px') if settings else '微软雅黑 22pt 正常 白色 描边黑色2px'
            font_name, font_size, font_weight, font_color, outline_color, outline_width, position = self._parse_subtitle_style_with_position(subtitle_style_str)

            # 构建字幕样式字符串
            style_str = f"Alignment={self._convert_position_to_alignment(position)},"

            if font_weight == "粗体":
                style_str += 'Bold=1,'
            else:
                style_str += 'Bold=0,'

            style_str += f"OutlineColour={self._convert_color_to_ffmpeg(outline_color)},"
            style_str += f"BorderStyle=1,Outline={outline_width},Shadow=0,"
            style_str += f"Fontsize={font_size},"
            style_str += f"FontName={font_name},"
            style_str += f"PrimaryColour={self._convert_color_to_ffmpeg(font_color)}"

            # 转义字幕路径
            escaped_subtitle_path = self._escape_path_for_ffmpeg_filter(subtitle_path)

            # 获取编码器设置
            codec, ffmpeg_params = self._get_optimal_encoder_settings(settings or {})

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 构建-vf备用命令（技术文档推荐的备用方案）
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-i', video_path,
                '-threads', '0',
                '-vf', f'subtitles={escaped_subtitle_path}:force_style=\'{style_str}\'',  # 使用-vf替代-lavfi
                '-c:v', codec,
                '-c:a', 'copy'
            ]

            # 添加编码器参数
            cmd.extend(ffmpeg_params)
            cmd.append(output_path)

            if progress_callback:
                progress_callback("启动-vf备用方案处理...")

            # 启动FFmpeg进程
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                video_duration = self.get_media_duration(video_path)
                self._monitor_ffmpeg_progress(progress_file.name, video_duration, progress_callback, "vf备用字幕")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            if process.returncode == 0:
                if progress_callback:
                    progress_callback("-vf备用方案字幕添加成功")
                return True
            else:
                if progress_callback:
                    progress_callback(f"-vf备用方案失败，尝试最简方案...")

                # 清理当前的临时文件
                try:
                    os.unlink(progress_file.name)
                except Exception:
                    pass

                # 最后的备用方案
                return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

        except Exception as e:
            if progress_callback:
                progress_callback(f"-vf备用方案异常: {str(e)}")
            return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

        finally:
            # 清理临时进度文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

    def _add_subtitles_fallback(self, video_path, subtitle_path, output_path, progress_callback=None, _is_retry=False):
        """备用字幕添加方法 - 使用最简单的参数"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        if progress_callback:
            if _is_retry:
                progress_callback("备用方案重试中...")
            else:
                progress_callback("使用备用方案添加字幕...")

        # 使用最简单的命令，强制软件编码
        cmd = [
            ffmpeg_exe, '-y',
            '-i', video_path,
            '-vf', f'subtitles={subtitle_path}',
            '-c:v', 'libx264',  # 强制使用软件编码器
            '-preset', 'medium',  # 使用中等预设
            '-c:a', 'copy',  # 复制音频
            output_path
        ]

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"备用字幕添加也失败: {stderr}")

            if progress_callback:
                progress_callback("备用方案字幕添加完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"备用方案失败: {str(e)}")
            raise e
    def _cleanup_debug_files(self, output_path, settings):
        """清理调试文件"""
        try:
            # 检查是否需要保留调试文件
            keep_debug_files = settings.get('keep_debug_files', False) if settings else False

            if keep_debug_files:
                return  # 保留调试文件

            # 获取输出目录
            output_dir = os.path.dirname(output_path)

            # 定义需要清理的调试文件列表
            debug_files = [
                "ffmpeg_loop_command.txt",
                "ffmpeg_merge_command.txt",
                "ffmpeg_bgm_command.txt",
                "ffmpeg_subtitle_command.txt",
                "ffmpeg_resize_command.txt",
                "ffmpeg_encode_command.txt"
            ]

            # 清理调试文件
            for debug_file in debug_files:
                debug_file_path = os.path.join(output_dir, debug_file)
                if os.path.exists(debug_file_path):
                    try:
                        os.unlink(debug_file_path)
                    except Exception:
                        pass  # 忽略清理失败

        except Exception:
            pass  # 忽略清理过程中的所有错误

    def add_subtitles_with_prerender(self, video_path, subtitle_path, output_path, settings=None, progress_callback=None):
        """
        使用预渲染模式添加字幕
        先将字幕渲染成透明PNG图片序列，然后使用overlay滤镜叠加到视频上
        这种方法可以显著提高字幕处理速度
        """
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                raise Exception("FFmpeg不可用")

            # 检查PIL可用性
            try:
                from PIL import Image, ImageDraw, ImageFont
                pil_available = True
                if progress_callback:
                    progress_callback("字幕预渲染模式: PIL库可用，使用高质量渲染")
            except ImportError:
                pil_available = False
                if progress_callback:
                    progress_callback("字幕预渲染模式: PIL库不可用，使用FFmpeg渲染")

            if progress_callback:
                progress_callback("字幕预渲染模式: 开始处理...")

            # 获取视频信息
            video_duration = self.get_media_duration(video_path)
            video_info = self._get_video_info(video_path)
            video_width = video_info.get('width', 1920)
            video_height = video_info.get('height', 1080)
            video_fps = video_info.get('fps', 25)

            if progress_callback:
                progress_callback(f"视频信息: {video_width}x{video_height}, {video_fps}fps, {video_duration:.2f}秒")

            # 解析字幕文件
            subtitle_entries = self._parse_srt_file(subtitle_path)
            if not subtitle_entries:
                raise Exception("字幕文件解析失败或为空")

            if progress_callback:
                progress_callback(f"解析到 {len(subtitle_entries)} 条字幕")

            # 创建临时目录存放预渲染的字幕图片
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix='subtitle_prerender_')

            if progress_callback:
                progress_callback("字幕预渲染模式: 20% - 开始渲染字幕图片...")

            # 预渲染字幕为PNG图片序列
            subtitle_images_dir = self._render_subtitles_to_images(
                subtitle_entries, temp_dir, video_width, video_height,
                video_fps, video_duration, settings, progress_callback
            )

            if progress_callback:
                progress_callback("字幕预渲染模式: 60% - 字幕图片渲染完成，开始合成视频...")

            # 使用overlay滤镜将字幕图片叠加到视频上
            success = self._overlay_subtitle_images_to_video(
                video_path, subtitle_images_dir, output_path,
                video_fps, settings, progress_callback
            )

            if progress_callback:
                progress_callback("字幕预渲染模式: 100% - 字幕添加完成")

            # 清理临时文件
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except Exception:
                pass

            return success

        except Exception as e:
            if progress_callback:
                progress_callback(f"预渲染字幕失败: {str(e)}")
            # 如果预渲染失败，回退到传统方法
            if progress_callback:
                progress_callback("回退到传统字幕处理方法...")
            return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, True)

    def _parse_srt_file(self, subtitle_path):
        """解析SRT字幕文件"""
        try:
            with open(subtitle_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            return self._parse_srt_content(srt_content)
        except Exception as e:
            print(f"解析SRT文件失败: {e}")
            return []

    def _render_subtitles_to_images(self, subtitle_entries, temp_dir, video_width, video_height,
                                   video_fps, video_duration, settings, progress_callback=None):
        """
        将字幕渲染为透明PNG图片序列
        """
        try:
            import time
            start_time = time.time()

            ffmpeg_exe = self._get_ffmpeg_executable()

            # 解析字幕样式
            subtitle_style_str = settings.get('subtitle_style', '微软雅黑 22pt 正常 白色 描边黑色2px') if settings else '微软雅黑 22pt 正常 白色 描边黑色2px'
            font_name, font_size, font_weight, font_color, outline_color, outline_width, position = self._parse_subtitle_style_with_position(subtitle_style_str)

            # 创建字幕图片目录
            images_dir = os.path.join(temp_dir, 'subtitle_images')
            os.makedirs(images_dir, exist_ok=True)

            # 计算总帧数
            total_frames = int(video_duration * video_fps)

            # 对于长视频，使用采样方式减少图片数量
            if total_frames > 18000:  # 超过10分钟的视频（30fps）
                frame_step = max(1, total_frames // 9000)  # 最多生成9000张图片
                if progress_callback:
                    progress_callback(f"长视频优化: 每{frame_step}帧采样一次，共生成约{total_frames//frame_step}张图片")
            else:
                frame_step = 1

            if progress_callback:
                actual_frames = total_frames // frame_step
                progress_callback(f"字幕预渲染模式: 20% - 开始渲染 {actual_frames} 帧图片...")

            # 为每一帧创建字幕图片（优化版）
            processed_frames = 0
            last_progress_report = 0
            actual_total_frames = total_frames // frame_step

            # 计算进度更新间隔
            progress_interval = max(1, actual_total_frames // 50)  # 每2%更新一次

            for frame_num in range(0, total_frames, frame_step):
                current_time = frame_num / video_fps

                # 查找当前时间对应的字幕
                current_subtitle = None
                for entry in subtitle_entries:
                    if entry['start'] <= current_time <= entry['end']:
                        current_subtitle = entry['text']
                        break

                # 生成字幕图片
                image_path = os.path.join(images_dir, f"subtitle_{frame_num//frame_step:06d}.png")
                self._create_subtitle_image(
                    image_path, current_subtitle, video_width, video_height,
                    font_name, font_size, font_weight, font_color,
                    outline_color, outline_width, position
                )

                processed_frames += 1

                # 更新进度 - 更频繁的进度更新
                if progress_callback and (processed_frames % progress_interval == 0 or processed_frames == actual_total_frames):
                    current_progress = 20 + int((processed_frames / actual_total_frames) * 40)  # 20%-60%

                    # 避免重复报告相同的进度
                    if current_progress > last_progress_report:
                        # 计算预计剩余时间
                        elapsed_time = time.time() - start_time
                        if processed_frames > 0 and elapsed_time > 0:
                            estimated_total_time = elapsed_time * actual_total_frames / processed_frames
                            estimated_remaining = estimated_total_time - elapsed_time
                            if estimated_remaining > 0:
                                remaining_str = f" (预计剩余: {estimated_remaining:.0f}秒)"
                            else:
                                remaining_str = ""
                        else:
                            remaining_str = ""

                        progress_callback(f"字幕预渲染模式: {current_progress}% - 已渲染 {processed_frames}/{actual_total_frames} 帧{remaining_str}")
                        last_progress_report = current_progress

            return images_dir

        except Exception as e:
            print(f"渲染字幕图片失败: {e}")
            raise

    def _create_subtitle_image(self, image_path, subtitle_text, width, height,
                              font_name, font_size, font_weight, font_color,
                              outline_color, outline_width, position):
        """
        创建单个字幕图片（透明背景）
        """
        try:
            from PIL import Image, ImageDraw, ImageFont
            import os

            # 创建透明背景图片
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # 如果没有字幕文本，创建空白图片
            if not subtitle_text or not subtitle_text.strip():
                img.save(image_path, 'PNG')
                return

            # 颜色映射
            color_map = {
                'white': (255, 255, 255, 255), 'black': (0, 0, 0, 255),
                'red': (255, 0, 0, 255), 'green': (0, 255, 0, 255),
                'blue': (0, 0, 255, 255), 'yellow': (255, 255, 0, 255),
                'cyan': (0, 255, 255, 255), 'magenta': (255, 0, 255, 255)
            }

            text_color = color_map.get(font_color.lower(), (255, 255, 255, 255))
            outline_color_rgba = color_map.get(outline_color.lower(), (0, 0, 0, 255))

            # 尝试加载字体
            try:
                # Windows系统字体路径
                font_paths = [
                    f"C:/Windows/Fonts/{font_name}.ttf",
                    f"C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                    f"C:/Windows/Fonts/simhei.ttf",  # 黑体
                    f"C:/Windows/Fonts/arial.ttf"   # Arial
                ]

                font = None
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, font_size)
                        break

                if font is None:
                    font = ImageFont.load_default()

            except Exception:
                font = ImageFont.load_default()

            # 计算文本位置
            text_bbox = draw.textbbox((0, 0), subtitle_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            # 根据位置设置坐标
            position_map = {
                "底部居中": (width // 2 - text_width // 2, height - text_height - 50),
                "顶部居中": (width // 2 - text_width // 2, 50),
                "左下角": (50, height - text_height - 50),
                "右下角": (width - text_width - 50, height - text_height - 50),
                "左上角": (50, 50),
                "右上角": (width - text_width - 50, 50),
                "居中": (width // 2 - text_width // 2, height // 2 - text_height // 2)
            }

            x, y = position_map.get(position, position_map["底部居中"])

            # 绘制描边
            if outline_width > 0:
                for dx in range(-outline_width, outline_width + 1):
                    for dy in range(-outline_width, outline_width + 1):
                        if dx != 0 or dy != 0:
                            draw.text((x + dx, y + dy), subtitle_text, font=font, fill=outline_color_rgba)

            # 绘制主文本
            draw.text((x, y), subtitle_text, font=font, fill=text_color)

            # 保存图片
            img.save(image_path, 'PNG')

        except Exception as e:
            # 如果PIL不可用，使用FFmpeg创建空白图片
            print(f"创建字幕图片失败: {e}")
            self._create_blank_image_with_ffmpeg(image_path, width, height)

    def _create_blank_image_with_ffmpeg(self, image_path, width, height):
        """使用FFmpeg创建空白透明图片"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'lavfi',
                '-i', f'color=c=transparent:size={width}x{height}:duration=0.04',
                '-frames:v', '1',
                image_path
            ]

            import subprocess
            import platform
            if platform.system() == "Windows":
                subprocess.run(cmd, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                subprocess.run(cmd, capture_output=True)

        except Exception:
            # 最后的备用方案：创建一个最小的PNG文件
            with open(image_path, 'wb') as f:
                # 最小的透明PNG文件头
                f.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82')

    def _overlay_subtitle_images_to_video(self, video_path, subtitle_images_dir, output_path,
                                         video_fps, settings, progress_callback=None):
        """
        使用overlay滤镜将字幕图片叠加到视频上
        """
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()

            # 获取编码器设置
            codec, ffmpeg_params = self._get_optimal_encoder_settings(settings or {})

            if progress_callback:
                progress_callback(f"使用overlay滤镜合成视频... (编码器: {codec})")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 构建FFmpeg命令
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-i', video_path,  # 输入视频
                '-framerate', str(video_fps),
                '-i', os.path.join(subtitle_images_dir, 'subtitle_%06d.png'),  # 字幕图片序列
                '-filter_complex', '[0:v][1:v]overlay=0:0:format=auto[out]',  # overlay滤镜
                '-map', '[out]',  # 映射输出视频
                '-map', '0:a?',   # 映射音频（如果存在）
                '-c:v', codec,    # 视频编码器
                '-c:a', 'copy',   # 音频直接复制
                '-shortest'       # 以最短流为准
            ]

            # 添加编码器参数
            cmd.extend(ffmpeg_params)
            cmd.append(output_path)

            if progress_callback:
                progress_callback("开始overlay合成...")

            # 启动FFmpeg进程
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                video_duration = self.get_media_duration(video_path)
                self._monitor_ffmpeg_progress(progress_file.name, video_duration,
                                            lambda msg: progress_callback(f"字幕预渲染模式: 60-100% - {msg}"),
                                            "overlay合成")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 清理临时文件
            try:
                os.unlink(progress_file.name)
            except Exception:
                pass

            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            if process.returncode != 0:
                raise Exception(f"Overlay合成失败: {stderr}")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"Overlay合成失败: {str(e)}")
            return False

    def _get_video_info(self, video_path):
        """获取视频信息"""
        try:
            ffprobe_exe = self._get_ffprobe_executable()
            if not ffprobe_exe:
                # 如果没有ffprobe，返回默认值
                return {'width': 1920, 'height': 1080, 'fps': 25}

            import subprocess
            import json
            import platform

            cmd = [
                ffprobe_exe,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'v:0',
                video_path
            ]

            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                data = json.loads(result.stdout)
                if data.get('streams'):
                    stream = data['streams'][0]
                    width = stream.get('width', 1920)
                    height = stream.get('height', 1080)

                    # 解析帧率
                    fps = 25
                    if 'r_frame_rate' in stream:
                        fps_str = stream['r_frame_rate']
                        if '/' in fps_str:
                            num, den = fps_str.split('/')
                            fps = int(num) / int(den) if int(den) != 0 else 25
                        else:
                            fps = float(fps_str)

                    return {'width': width, 'height': height, 'fps': fps}

            # 如果解析失败，返回默认值
            return {'width': 1920, 'height': 1080, 'fps': 25}

        except Exception:
            return {'width': 1920, 'height': 1080, 'fps': 25}

    def _get_ffprobe_executable(self):
        """获取ffprobe可执行文件路径"""
        try:
            # 尝试从FFmpeg路径获取ffprobe
            ffmpeg_exe = self._get_ffmpeg_executable()
            if ffmpeg_exe:
                import os
                ffmpeg_dir = os.path.dirname(ffmpeg_exe)
                ffprobe_exe = os.path.join(ffmpeg_dir, 'ffprobe.exe' if os.name == 'nt' else 'ffprobe')
                if os.path.exists(ffprobe_exe):
                    return ffprobe_exe

            # 尝试系统PATH
            import shutil
            return shutil.which('ffprobe')

        except Exception:
            return None

    def _create_subtitle_timeline(self, subtitle_entries, video_duration, video_fps):
        """
        创建字幕时间轴映射，将每个时间点映射到对应的字幕文本
        这样可以避免为每一帧都生成图片，只为有字幕变化的地方生成
        """
        timeline = {}

        # 以0.1秒为单位创建时间轴（比帧更粗粒度，减少计算量）
        time_step = 0.1
        total_steps = int(video_duration / time_step) + 1

        for step in range(total_steps):
            current_time = step * time_step

            # 查找当前时间对应的字幕
            current_subtitle = ""
            for entry in subtitle_entries:
                if entry['start'] <= current_time <= entry['end']:
                    current_subtitle = entry['text']
                    break

            timeline[current_time] = current_subtitle

        return timeline

    def _create_subtitle_sequence_file(self, images_dir, subtitle_map, subtitle_images, video_fps):
        """
        创建字幕序列文件，用于告诉FFmpeg每个时间点使用哪个字幕图片
        """
        try:
            # 创建一个简单的映射文件
            sequence_file = os.path.join(images_dir, "subtitle_sequence.txt")

            with open(sequence_file, 'w', encoding='utf-8') as f:
                f.write("# 字幕序列映射文件\n")
                f.write("# 格式: 时间(秒) 图片文件名\n")

                for time_point in sorted(subtitle_map.keys()):
                    subtitle_text = subtitle_map[time_point]
                    image_filename = subtitle_images.get(subtitle_text, "subtitle_empty.png")
                    f.write(f"{time_point:.1f} {image_filename}\n")

            return sequence_file

        except Exception as e:
            print(f"创建字幕序列文件失败: {e}")
            return None

    def _convert_position_to_alignment(self, position):
        """将位置转换为FFmpeg字幕对齐方式"""
        position_map = {
            "底部居中": "2",
            "顶部居中": "8",
            "左下角": "1",
            "右下角": "3",
            "左上角": "7",
            "右上角": "9",
            "居中": "5"
        }
        return position_map.get(position, "2")  # 默认底部居中

    def _convert_color_to_ffmpeg(self, color):
        """将颜色名称转换为FFmpeg格式"""
        color_map = {
            'white': '&Hffffff',
            'black': '&H000000',
            'red': '&H0000ff',
            'green': '&H00ff00',
            'blue': '&Hff0000',
            'yellow': '&H00ffff',
            'cyan': '&Hffff00',
            'magenta': '&Hff00ff',
            '白色': '&Hffffff',
            '黑色': '&H000000',
            '红色': '&H0000ff',
            '绿色': '&H00ff00',
            '蓝色': '&Hff0000',
            '黄色': '&H00ffff',
            '青色': '&Hffff00',
            '紫色': '&Hff00ff'
        }
        return color_map.get(color.lower(), '&Hffffff')

    def _escape_path_for_ffmpeg_filter(self, path):
        """为FFmpeg滤镜转义路径"""
        # Windows路径处理
        if os.name == 'nt':
            # 将反斜杠转换为正斜杠
            path = path.replace('\\', '/')
            # 转义冒号
            path = path.replace(':', '\\:')
        return path
