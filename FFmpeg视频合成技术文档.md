# FFmpeg视频合成技术文档

## 概述

本文档详细介绍了基于FFmpeg的视频自动合成工具的核心技术实现，包括多线程处理、硬件加速、视频合并、音频处理、字幕添加等关键功能。

## 核心架构

### 主要类结构

1. **VideoProcessingThread** - 视频处理主线程类
2. **VideoProcessingApp** - GUI应用程序主类  
3. **SubtitleSettingsDialog** - 字幕设置对话框类
4. **Logger** - 日志记录类

## 多线程处理架构

### 1. 主处理线程 (VideoProcessingThread)

```python
class VideoProcessingThread(threading.Thread):
    def __init__(self, target_file, subfolders, options, update_progress, completion_callback):
        super(VideoProcessingThread, self).__init__()
        self.target_file = target_file
        self.subfolders = subfolders
        self.options = options
        self.update_progress = update_progress
        self.completion_callback = completion_callback
        self.is_canceled = False
        self.temp_dirs = []
        # 创建临时目录
        output_dir = self.options.get('output_dir')
        self.temp_dir = tempfile.mkdtemp(prefix='文运躺音_', dir=output_dir if output_dir else None)
        self.temp_dirs.append(self.temp_dir)
        self.logger = Logger()
```

### 2. 并行处理实现

```python
def run(self):
    # 并行处理模式
    if self.options.get('parallel_processing', True) and max_workers > 1:
        self.logger.info('使用并行处理模式处理全局任务队列')
        futures_dict = {}
        pending_tasks = global_tasks.copy()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            active_futures = set()
            while pending_tasks or active_futures:
                # 提交新任务
                while len(active_futures) < self.max_workers and pending_tasks:
                    task = pending_tasks.pop(0)
                    subfolder, audio_file, audio_index, subfolder_index = task
                    if self.is_canceled:
                        break
                    
                    future = executor.submit(
                        self.process_audio_file_with_isolation, 
                        subfolder, info['audio_folder'], info['video_folder'], 
                        info['output_dir'], audio_file, audio_index, 
                        processed_audio_count, total_audio_files, info['video_files']
                    )
                    futures_dict[future] = (subfolder, audio_file)
                    active_futures.add(future)
                
                # 等待任务完成
                if active_futures:
                    done, not_done = concurrent.futures.wait(
                        active_futures, timeout=0.5, 
                        return_when=concurrent.futures.FIRST_COMPLETED
                    )
                    
                    for future in done:
                        subfolder, audio_file = futures_dict[future]
                        try:
                            result = future.result()
                            if result:
                                processed_videos_by_subfolder[subfolder].append(result)
                                processed_audio_count += 1
                        except Exception as e:
                            self.logger.error(f'处理音频文件出错: {str(e)}')
                        
                        active_futures.remove(future)
                        del futures_dict[future]
```

### 3. 任务隔离处理

```python
def process_audio_file_with_isolation(self, subfolder, audio_folder, video_folder, 
                                    output_dir, audio_file, audio_index, 
                                    processed_audio_count, total_audio_files, video_files):
    """在隔离环境中处理单个音频文件，包括创建和清理独立的临时目录"""
    audio_name_for_dir = os.path.splitext(os.path.basename(audio_file))[0]
    task_id = f'{audio_name_for_dir}_{uuid.uuid4().hex[:8]}'
    task_temp_dir = os.path.join(self.temp_dir, f'task_{task_id}')
    os.makedirs(task_temp_dir, exist_ok=True)
    
    try:
        return self.process_audio_file(
            subfolder, audio_folder, video_folder, output_dir, 
            audio_file, audio_index, processed_audio_count, 
            total_audio_files, video_files, thread_temp_dir=task_temp_dir
        )
    finally:
        try:
            if os.path.exists(task_temp_dir):
                shutil.rmtree(task_temp_dir)
                self.logger.debug(f'清理了任务临时目录: {task_temp_dir}')
        except Exception as e:
            self.logger.error(f'清理任务临时目录失败: {e}')
```

## 硬件加速检测与配置

### 1. GPU检测函数

```python
def check_nvidia_gpu(self):
    """检测NVIDIA GPU"""
    try:
        if platform.system() == 'Windows':
            result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE, text=True, 
                                  startupinfo=self.startupinfo, 
                                  creationflags=self.creation_flags)
        else:
            result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE, text=True)
        return result.returncode == 0
    except:
        return False

def check_intel_gpu(self):
    """检测Intel GPU"""
    try:
        if platform.system().lower() == 'windows':
            result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, 
                                  startupinfo=self.startupinfo, creationflags=self.creation_flags)
            return 'Intel' in result.stdout
        result = subprocess.run(['lspci'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        return 'Intel' in result.stdout and 'VGA' in result.stdout
    except:
        return False

def check_amd_gpu(self):
    """检测AMD GPU"""
    try:
        if platform.system().lower() == 'windows':
            result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, 
                                  startupinfo=self.startupinfo, creationflags=self.creation_flags)
            if 'AMD' in result.stdout or 'Radeon' in result.stdout or 'RX' in result.stdout:
                return True
        return False
    except:
        return False
```

### 2. 最优编码器选择

```python
def _get_optimal_encoder(self):
    """获取最佳编码器配置，优化GPU利用率"""
    bitrate = '2000k'
    if self.options.get('custom_bitrate', False):
        bitrate_value = self.options.get('bitrate', '2000')
        if int(bitrate_value) >= 1000:
            bitrate = f'{int(bitrate_value) - 1000}M'
        else:
            bitrate = f'{bitrate_value}k'
    
    if self.options.get('hardware_acceleration', False):
        system_info = platform.system().lower()
        
        if system_info == 'windows':
            # NVIDIA GPU加速
            if self.check_nvidia_gpu():
                self.logger.info('检测到NVIDIA GPU，使用NVENC硬件加速')
                return ('h264_nvenc', ['-preset', 'p1', '-profile:v', 'high', 
                                     '-rc', 'vbr', '-cq', '30', '-qmin', '15', 
                                     '-qmax', '28', '-b:v', bitrate, 
                                     '-nonref_p', '1', '-strict_gop', '1'])
            
            # Intel GPU加速
            if self.check_intel_gpu():
                self.logger.info('检测到Intel GPU，使用QSV硬件加速')
                return ('h264_qsv', ['-preset', 'medium', '-profile:v', 'high', 
                                   '-global_quality', '23', '-look_ahead', '0', 
                                   '-b:v', bitrate, '-low_power', '0', 
                                   '-num_ref_frame', '5', '-g', '50', 
                                   '-threads', '0', '-thread_queue_size', '2048'])
            
            # AMD GPU加速
            if self.check_amd_gpu():
                self.logger.info('检测到AMD GPU，使用AMF硬件加速')
                return ('h264_amf', ['-quality', 'speed', '-b:v', bitrate, 
                                   '-usage', 'transcoding', '-threads', '0', 
                                   '-thread_queue_size', '2048'])
        
        elif system_info == 'linux':
            if self.check_nvidia_gpu():
                return ('h264_nvenc', ['-preset', 'p5', '-rc', 'vbr', 
                                     '-crf', '30', '-b:v', bitrate, 
                                     '-threads', '0', '-thread_queue_size', '2048'])
            
            if self.check_vaapi():
                self.logger.info('检测到VAAPI，使用VAAPI硬件加速')
                return ('h264_vaapi', ['-vaapi_device', '/dev/dri/renderD128', 
                                     '-global_quality', '27', '-b:v', bitrate, 
                                     '-threads', '0', '-thread_queue_size', '2048'])
        
        elif system_info == 'darwin':
            self.logger.info('在macOS上使用VideoToolbox硬件加速')
            return ('h264_videotoolbox', ['-q', '50', '-b:v', bitrate, 
                                        '-threads', '0', '-thread_queue_size', '2048'])
    
    # 软件编码器
    self.logger.info('使用软件编码器')
    return ('libx264', ['-preset', 'medium', '-profile:v', 'high', '-crf', '30', 
                       '-pix_fmt', 'yuv420p', '-b:v', bitrate, '-tune', 'fastdecode', 
                       '-direct-pred', 'spatial', '-partitions', 'i8x8,i4x4', 
                       '-refs', '3', '-subq', '4', '-trellis', '0', 
                       '-weightb', '0', '-threads', '0'])
```

## 核心视频处理功能

### 1. 一步合成视频与音频

```python
def process_video_with_audio(self, input_video, audio_path, output_path):
    """一步完成：创建循环视频并合并音频"""
    try:
        if not os.path.exists(input_video):
            self.logger.error(f'输入视频文件不存在: {input_video}')
            return False
        if not os.path.exists(audio_path):
            self.logger.error(f'输入音频文件不存在: {audio_path}')
            return False
        
        # 获取音频时长
        audio_info = self._get_video_info(audio_path)
        audio_duration = float(audio_info.get('duration', 0))
        if audio_duration <= 0:
            self.logger.error(f'无法获取音频时长: {audio_path}')
            return False
        
        self.logger.info(f'音频时长: {audio_duration}秒')
        
        # 获取编码器配置
        hw_encoder = self._get_optimal_encoder()
        hw_options = []
        
        # 分辨率设置
        resolution_width = '1280'
        resolution_height = '720'
        if self.options.get('custom_resolution', False):
            resolution_width = self.options.get('resolution_width', '1280')
            resolution_height = self.options.get('resolution_height', '720')
        
        # 码率设置
        bitrate = '2000k'
        if self.options.get('custom_bitrate', False):
            bitrate_value = self.options.get('bitrate', '2000')
            bitrate = f'{bitrate_value}k'
        
        # 硬件编码器选择
        if self.check_nvidia_gpu():
            hw_encoder = 'h264_nvenc'
            hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-b:v', bitrate]
        elif self.check_intel_gpu():
            hw_encoder = 'h264_qsv'
            hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-b:v', bitrate]
        elif self.check_amd_gpu():
            hw_encoder = 'h264_amf'
            hw_options = ['-c:v', hw_encoder, '-quality', 'speed', '-b:v', bitrate, '-usage', 'transcoding']
        else:
            hw_encoder = 'libx264'
            hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-crf', '30', '-b:v', bitrate]
        
        # 构建FFmpeg命令
        cmd = ['ffmpeg', '-y', '-stream_loop', '-1', '-i', f'"{input_video}"', 
               '-i', f'"{audio_path}"', '-t', str(audio_duration), 
               '-vf', f'scale={resolution_width}:{resolution_height}', 
               '-c:a', 'aac', '-b:a', '256k', '-map', '0:v', '-map', '1:a', '-shortest']
        cmd.extend(hw_options)
        cmd.append(f'"{output_path}"')
        
        cmd_str = ' '.join(cmd)
        if platform.system() == 'Windows':
            cmd_str = f'cmd /c {cmd_str}'
        
        self.logger.info(f'执行一步合成命令: {cmd_str}')
        
        # 执行命令
        if platform.system() == 'Windows':
            process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, universal_newlines=True, 
                                     encoding='utf-8', errors='replace', 
                                     startupinfo=self.startupinfo, 
                                     creationflags=self.creation_flags)
        else:
            process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, universal_newlines=True, 
                                     encoding='utf-8', errors='replace')
        
        # 监控进度
        start_time = time.time()
        last_update_time = start_time
        while process.poll() is None:
            if process.stderr:
                line = process.stderr.readline()
                if line:
                    last_update_time = time.time()
                    self._extract_and_log_ffmpeg_progress(line.strip())
                else:
                    time.sleep(0.1)
            else:
                time.sleep(0.1)
        
        result = process.wait()
        if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            self.logger.info(f'一步合成视频成功: {output_path}')
            return True
        else:
            self.logger.error(f'一步合成视频失败，返回码: {result}')
            return False
            
    except Exception as e:
        self.logger.error(f'一步合成视频时出错: {str(e)}')
        return False
```

### 2. 视频合并功能

```python
def merge_videos(self, video_files, output_path):
    """合并多个视频文件为一个视频"""
    if not video_files or len(video_files) < 2:
        self.logger.error('需要至少两个视频文件进行合并')
        return False

    self.logger.info(f"开始合并视频: {', '.join(video_files)} 到 {output_path}")
    total_videos = len(video_files)

    # 创建临时目录
    output_dir = os.path.dirname(output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    target_temp_dir = os.path.join(output_dir, f'临时_{int(time.time())}')
    os.makedirs(target_temp_dir, exist_ok=True)
    self.temp_dirs.append(target_temp_dir)

    try:
        # 验证视频文件
        valid_videos = []
        for i, video in enumerate(video_files):
            if os.path.exists(video) and os.path.getsize(video) > 0:
                valid_videos.append(video)
                self.update_progress(60 + int((i + 1) / total_videos * 20),
                                   f'验证视频文件 {i + 1}/{total_videos}...')
            else:
                self.logger.error(f'视频文件不存在或为空: {video}')

        if len(valid_videos) < 2:
            self.logger.error('没有足够的有效视频进行合并')
            return False

        # 创建视频列表文件
        list_file_path = os.path.join(target_temp_dir, 'merge_list.txt')
        with open(list_file_path, 'w', encoding='utf-8') as f:
            for i, video in enumerate(valid_videos):
                video_path = video.replace('\\', '/')
                f.write(f'file \'{video_path}\'\n')
                self.update_progress(80 + int((i + 1) / len(valid_videos) * 10),
                                   f'准备合并列表 {i + 1}/{len(valid_videos)}...')

        # 执行合并命令
        merge_cmd = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0',
                    '-i', f'"{list_file_path}"', '-c', 'copy',
                    '-max_muxing_queue_size', '9999', '-movflags', '+faststart',
                    f'"{output_path}"']
        merge_cmd_str = ' '.join(merge_cmd)

        if platform.system() == 'Windows':
            merge_cmd_str = f'cmd /c {merge_cmd_str}'

        self.logger.info(f'执行合并命令: {merge_cmd_str}')

        if platform.system() == 'Windows':
            process = subprocess.Popen(merge_cmd_str, shell=True,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     universal_newlines=True, encoding='utf-8',
                                     errors='replace', startupinfo=self.startupinfo,
                                     creationflags=self.creation_flags)
        else:
            process = subprocess.Popen(merge_cmd_str, shell=True,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     universal_newlines=True, encoding='utf-8',
                                     errors='replace')

        # 监控合并进度
        start_time = time.time()
        while process.poll() is None:
            if process.stderr:
                line = process.stderr.readline()
                if line:
                    if 'Non-monotonous' in line or 'timestamp' in line:
                        self.logger.warning(f'时间戳警告: {line.strip()}')
                    if 'error' in line.lower() or 'could not' in line.lower():
                        self.logger.warning(f'错误信息: {line.strip()}')
                    self._extract_and_log_ffmpeg_progress(line.strip())
            else:
                time.sleep(0.1)

        result = process.wait()
        if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            self.logger.info(f'视频合并成功: {output_path}')
            return True
        else:
            self.logger.error(f'视频合并失败，返回码: {result}')
            return False

    except Exception as e:
        self.logger.error(f'合并视频时发生错误: {str(e)}')
        return False
```

### 3. BGM添加功能

```python
def _add_bgm_to_video(self, input_video, output_path, bgm_path, volume=0.2):
    """添加背景音乐到视频，使用高性能方法"""
    self.logger.info(f'添加背景音乐: {os.path.basename(bgm_path)} 到 {os.path.basename(input_video)}')

    try:
        # 获取视频时长
        video_info = self._get_video_info(input_video)
        video_duration = float(video_info.get('duration', 0))
        if video_duration <= 0:
            self.logger.error('无法获取视频时长，无法添加背景音乐')
            return False

        # BGM模式处理
        bgm_mode = self.options.get('bgm_mode', 'loop')
        self.logger.info(f'BGM模式: {bgm_mode}')

        if bgm_mode == 'random' and os.path.isdir(bgm_path):
            # 随机BGM模式
            bgm_concat_file = self._create_bgm_concat_file(bgm_path, video_duration,
                                                         os.path.dirname(output_path))
            if not bgm_concat_file:
                self.logger.error('创建BGM连接文件失败')
                return False
            bgm_file_to_use = bgm_path
        else:
            bgm_file_to_use = bgm_path

        # 计算BGM循环次数
        try:
            bgm_info = self._get_video_info(bgm_file_to_use)
            bgm_duration = float(bgm_info.get('duration', 0))
            if bgm_duration <= 0:
                loop_count = -1
            else:
                loop_count = math.ceil(video_duration / bgm_duration)
                self.logger.info(f'视频时长: {video_duration}秒, BGM时长: {bgm_duration}秒, 需要循环: {loop_count}次')
        except Exception as e:
            self.logger.warning(f'计算BGM循环次数时出错: {str(e)}，使用默认循环')
            loop_count = -1

        # 获取硬件编码器
        hw_encoder, hw_options = self._get_optimal_encoder()

        # 构建FFmpeg命令 - 使用视频直接复制模式
        cmd_parts = [
            'ffmpeg', '-y', '-i', f'"{input_video}"',
            '-stream_loop', f'{loop_count}', '-i', f'"{bgm_file_to_use}"',
            '-filter_complex',
            f'[1:a]volume={volume}[bgm];[0:a][bgm]amix=inputs=2:duration=shortest:normalize=0[aout]',
            '-map', '0:v', '-map', '[aout]', '-c:v', 'copy', '-c:a', 'libmp3lame',
            '-q:a', '9', '-compression_level', '0', '-shortest',
            '-max_muxing_queue_size', '4096', '-fflags', '+genpts',
            '-flags', '+low_delay', '-preset', 'ultrafast', '-tune', 'zerolatency',
            '-movflags', '+faststart', f'"{output_path}"'
        ]

        if platform.system() == 'Windows':
            clean_cmd = []
            for part in cmd_parts:
                clean_part = part.replace('"', '')
                clean_cmd.append(clean_part)

            try:
                process = subprocess.Popen(clean_cmd, stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE, universal_newlines=True,
                                         encoding='utf-8', errors='replace',
                                         startupinfo=self.startupinfo,
                                         creationflags=self.creation_flags)
            except Exception as e:
                self.logger.error(f'启动BGM添加进程时出错: {str(e)}')
                cmd_str = ' '.join(cmd_parts)
                cmd_str = f'cmd /c {cmd_str}'
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE, universal_newlines=True,
                                         encoding='utf-8', errors='replace',
                                         startupinfo=self.startupinfo,
                                         creationflags=self.creation_flags)
        else:
            cmd_str = ' '.join(cmd_parts)
            process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, universal_newlines=True,
                                     encoding='utf-8', errors='replace')

        # 监控进度
        start_time = time.time()
        last_update_time = start_time
        while process.poll() is None:
            if process.stderr:
                line = process.stderr.readline()
                if line:
                    last_update_time = time.time()
                    self._extract_and_log_ffmpeg_progress(line.strip())
                else:
                    time.sleep(0.1)
            else:
                time.sleep(0.1)

        result = process.wait()
        if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            self.logger.info(f'背景音乐添加成功: {os.path.basename(output_path)}')
            return True
        else:
            self.logger.error(f'背景音乐添加失败，ffmpeg返回码: {result}')
            return False

    except Exception as e:
        self.logger.error(f'添加背景音乐时出错: {str(e)}')
        return False
```

### 4. 字幕添加功能

```python
def _add_subtitles_with_moviepy(self, input_video, output_path, subtitle_path):
    """使用简单的ffmpeg命令添加字幕，不依赖ImageMagick和其他复杂库"""
    self.logger.info(f'使用简单ffmpeg方法添加字幕: {subtitle_path} 到 {input_video}')
    temp_subtitle_path = None

    try:
        subtitle_style = self.options.get('subtitle_style', {})
        self.logger.info(f'应用字幕样式: {subtitle_style}')

        # 码率设置
        bitrate = '2000k'
        if self.options.get('custom_bitrate', False):
            bitrate_value = self.options.get('bitrate', '2000')
            bitrate = f'{bitrate_value}k'

        # 获取硬件编码器
        hw_encoder, hw_options = self._get_optimal_encoder()

        # 创建唯一的临时字幕文件
        temp_dir = os.path.dirname(output_path)
        unique_subtitle_name = f'temp_subtitle_{uuid.uuid4().hex[:8]}.srt'
        temp_subtitle_path = os.path.join(temp_dir, unique_subtitle_name)

        try:
            shutil.copy2(subtitle_path, temp_subtitle_path)
            self.logger.info(f'复制字幕文件到唯一的临时位置: {temp_subtitle_path}')
        except Exception as e:
            self.logger.error(f'致命错误：无法复制字幕到临时位置: {str(e)}，跳过字幕添加。')
            shutil.copy2(input_video, output_path)
            return False

        # 转义字幕路径
        escaped_subtitle_path = self._escape_path_for_ffmpeg_filter(temp_subtitle_path)

        if platform.system() == 'Windows':
            # 构建字幕样式字符串
            style_str = f"Alignment={self._convert_position_to_alignment(subtitle_style.get('position', '底部'))},"

            if subtitle_style.get('bold', False):
                style_str += 'Bold=1,'
            else:
                style_str += 'Bold=0,'

            if subtitle_style.get('italic', False):
                style_str += 'Italic=1,'
            else:
                style_str += 'Italic=0,'

            style_str += f"OutlineColour={self._convert_color_to_ffmpeg(subtitle_style.get('outline_color', '黑色'))},"
            style_str += f"BorderStyle=1,Outline={subtitle_style.get('outline_width', 1.0)},Shadow=0,"
            style_str += f"Fontsize={subtitle_style.get('font_size', 22)},"
            style_str += f"FontName={subtitle_style.get('font', '微软雅黑')},"
            style_str += f"PrimaryColour={self._convert_color_to_ffmpeg(subtitle_style.get('font_color', '白色'))}"

            self.logger.info(f'使用字幕样式: {style_str}')

            # 编码器选项
            encoder_options = ''
            if hw_encoder == 'h264_nvenc':
                encoder_options = f'-c:v {hw_encoder} -preset p1 -rc vbr -crf 30 -b:v {bitrate}'
            elif hw_encoder == 'h264_qsv':
                encoder_options = f'-c:v {hw_encoder} -preset medium -global_quality 27 -b:v {bitrate}'
            elif hw_encoder == 'h264_amf':
                encoder_options = f'-c:v {hw_encoder} -quality speed -usage transcoding -b:v {bitrate}'
            else:
                encoder_options = f'-c:v libx264 -preset medium -crf 30 -b:v {bitrate}'

            # 构建FFmpeg命令
            cmd = f'cmd /c ffmpeg -y -i "{input_video}" -threads 0 -lavfi "subtitles={escaped_subtitle_path}:force_style=\'{style_str}\'" {encoder_options} -c:a copy "{output_path}"'

        self.logger.info(f'执行添加字幕命令: {cmd}')

        # 执行命令
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE, universal_newlines=True,
                                 encoding='utf-8', errors='replace',
                                 startupinfo=self.startupinfo,
                                 creationflags=self.creation_flags,
                                 stdin=subprocess.DEVNULL)

        # 监控进度
        start_time = time.time()
        last_update_time = start_time
        while process.poll() is None:
            if process.stderr:
                line = process.stderr.readline()
                if line:
                    last_update_time = time.time()
                    self._extract_and_log_ffmpeg_progress(line.strip())
                else:
                    time.sleep(0.1)
            else:
                time.sleep(0.1)

        result = process.wait()
        if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            self.logger.info(f'字幕添加成功: {output_path}')
            try:
                os.remove(temp_subtitle_path)
                self.logger.info(f'已清理临时字幕文件: {temp_subtitle_path}')
            except Exception as e:
                self.logger.warning(f'清理临时字幕文件时出错: {str(e)}')
            return True

        # 如果失败，尝试备用方法
        self.logger.error(f'字幕添加失败，返回码: {result}')
        self.logger.info('尝试使用-vf替代-lavfi...')

        simple_cmd = f'cmd /c ffmpeg -y -i "{input_video}" -threads 0 -vf "subtitles={escaped_subtitle_path}" {encoder_options} -c:a copy "{output_path}"'

        if os.path.exists(output_path):
            try:
                os.remove(output_path)
            except:
                pass

        try:
            self.logger.info(f'执行简单备用命令: {simple_cmd}')
            backup_process = subprocess.Popen(simple_cmd, shell=True,
                                            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                            universal_newlines=True, encoding='utf-8',
                                            errors='replace', startupinfo=self.startupinfo,
                                            creationflags=self.creation_flags,
                                            stdin=subprocess.DEVNULL)

            while backup_process.poll() is None:
                if backup_process.stderr:
                    line = backup_process.stderr.readline()
                    if line:
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)

            backup_result = backup_process.wait()
            if backup_result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f'备用字幕添加方法成功: {output_path}')
                try:
                    if temp_subtitle_path and os.path.exists(temp_subtitle_path):
                        os.remove(temp_subtitle_path)
                        self.logger.info(f'已清理临时字幕文件: {temp_subtitle_path}')
                except Exception as e:
                    self.logger.warning(f'清理临时字幕文件时出错: {str(e)}')
                return True
            else:
                self.logger.error(f'备用字幕添加方法失败，返回码: {backup_result}')
                if os.path.exists(input_video):
                    shutil.copy2(input_video, output_path)
                return False

        except Exception as backup_e:
            self.logger.error(f'备用字幕方法执行时出错: {str(backup_e)}')
            if os.path.exists(input_video):
                shutil.copy2(input_video, output_path)
            return False

    except Exception as e:
        self.logger.error(f'字幕添加过程中发生错误: {str(e)}')
        if os.path.exists(input_video):
            shutil.copy2(input_video, output_path)
        return False
```

### 5. 进度监控与日志记录

```python
def _extract_and_log_ffmpeg_progress(self, line):
    """从FFmpeg输出中提取进度信息并记录到日志"""
    try:
        progress_info = {}
        current_time = time.time()

        # 限制进度更新频率
        if hasattr(self, '_last_ffmpeg_progress_update') and \
           current_time - self._last_ffmpeg_progress_update < 3.0:
            self._last_ffmpeg_progress_update = current_time
        else:
            return False

        # 提取各种进度信息
        frame_match = re.search(r'frame=\s*(\d+)', line)
        if frame_match:
            progress_info['frame'] = frame_match.group(1)

        fps_match = re.search(r'fps=\s*(\d+)', line)
        if fps_match:
            progress_info['fps'] = fps_match.group(1)

        time_match = re.search(r'time=(\d+):(\d+):(\d+\.\d+)', line)
        if time_match:
            h, m, s = time_match.groups()
            progress_info['time'] = f'{h}:{m}:{s}'

        bitrate_match = re.search(r'bitrate=\s*(\d+\.\d+)', line)
        if bitrate_match:
            progress_info['bitrate'] = f'{bitrate_match.group(1)}kbits/s'

        size_match = re.search(r'size=\s*(\d+)(\w+)', line)
        if size_match:
            size, unit = size_match.groups()
            progress_info['size'] = f'{size}{unit}'

        speed_match = re.search(r'speed=\s*(\S+)', line)
        if speed_match:
            speed_val = speed_match.group(1)
            if speed_val.endswith('x'):
                progress_info['speed'] = speed_val
            else:
                progress_info['speed'] = f'{speed_val}'

        q_match = re.search(r'q=\s*(\d+\.\d+)', line)
        if q_match:
            progress_info['q'] = q_match.group(1)

        # 构建进度字符串
        if 'frame' in progress_info:
            progress_str = '[进度] '
            progress_str += 'frame=' + str(progress_info.get('frame', '?'))

            if 'fps' in progress_info:
                progress_str += f" fps={progress_info['fps']}"
            if 'q' in progress_info:
                progress_str += f" q={progress_info['q']}"
            if 'size' in progress_info:
                progress_str += f" size={progress_info['size']}"
            if 'time' in progress_info:
                progress_str += f" time={progress_info['time']}"
            if 'bitrate' in progress_info:
                progress_str += f" bitrate={progress_info['bitrate']}"
            if 'speed' in progress_info:
                progress_str += f" speed={progress_info['speed']}"

            self.logger.info(progress_str)
            self.update_progress(None, progress_str)
            return True
        else:
            # 处理其他进度信息
            if 'frame=' in line or 'fps=' in line or 'time=' in line or \
               'size=' in line or 'bitrate=' in line:
                progress_str = f'[进度] ' + line.strip()
                self.logger.info(progress_str)
                self.update_progress(None, progress_str)
                return True

    except Exception as e:
        self.logger.warning(f'解析FFmpeg进度时出错: {str(e)}')
        return False
```

## 工具函数

### 1. 视频信息获取

```python
def _get_video_info(self, video_path):
    """获取视频信息，包括时长等"""
    try:
        cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration,size',
               '-show_entries', 'stream=width,height,codec_name', '-of', 'json', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True,
                              encoding='utf-8', errors='replace')

        if result.returncode != 0:
            self.logger.warning(f'获取视频信息失败，返回码: {result.returncode}')
            self.logger.warning(f'错误信息: {result.stderr}')
            return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}

        info = json.loads(result.stdout)
        video_info = {}

        if 'format' in info:
            video_info['duration'] = info['format'].get('duration', '0')
            video_info['size'] = info['format'].get('size', '0')

        if 'streams' in info:
            for stream in info['streams']:
                if stream.get('codec_type') == 'video':
                    video_info['width'] = stream.get('width', '0')
                    video_info['height'] = stream.get('height', '0')
                    video_info['codec_name'] = stream.get('codec_name', 'unknown')
                    break

        return video_info

    except Exception as e:
        self.logger.error(f'获取视频信息时出错: {str(e)}')
        return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}
```

### 2. 路径转义处理

```python
def _escape_path_for_ffmpeg_filter(self, path):
    """为FFmpeg的-vf/-lavfi滤镜转义Windows路径"""
    if platform.system() == 'Windows':
        return path.replace('\\', '/').replace(':', '\\\\:')
    return path
```

### 3. 颜色转换函数

```python
def _convert_color_to_ffmpeg(self, color_name):
    """将中文颜色名称转换为FFmpeg可用的十六进制颜色代码"""
    color_map = {
        '白色': '&HFFFFFF', '黑色': '&H000000', '红色': '&H0000FF',
        '绿色': '&H00FF00', '蓝色': '&HFF0000', '黄色': '&H00D7FF',
        '青色': '&HFFFF00', '洋红': '&HFF00FF'
    }
    return color_map.get(color_name, '&H00FFFFFF')

def _convert_position_to_alignment(self, position):
    """将位置名称转换为ASS文件中的alignment值（数字1-9）"""
    position_map = {
        '左上角': 7, '顶部': 8, '右上角': 9,
        '左侧': 4, '中间': 10, '右侧': 6,
        '左下角': 1, '底部': 2, '右下角': 3
    }
    return position_map.get(position, 2)
```

### 4. 临时目录清理

```python
def _clean_temp_dirs(self):
    """清理临时目录"""
    if not hasattr(self, 'temp_dirs') or not self.temp_dirs:
        return None

    self.logger.info(f'开始清理临时目录，总数: {len(self.temp_dirs)}')
    all_temp_dirs = []

    for temp_dir in self.temp_dirs:
        if temp_dir and os.path.exists(temp_dir):
            all_temp_dirs.append(temp_dir)

    # 搜索额外的临时目录
    try:
        for subfolder in self.subfolders:
            if os.path.exists(subfolder):
                output_dir = os.path.join(subfolder, '处理完成')
                if os.path.exists(output_dir):
                    for item in os.listdir(output_dir):
                        item_path = os.path.join(output_dir, item)
                        if os.path.isdir(item_path) and item.startswith('临时_') and \
                           item_path not in all_temp_dirs:
                            all_temp_dirs.append(item_path)
                            self.logger.info(f'找到额外的临时目录: {item_path}')
    except Exception as e:
        self.logger.warning(f'搜索额外临时目录时出错: {str(e)}')

    self.logger.info(f'共找到 {len(all_temp_dirs)} 个临时目录需要清理')

    for temp_dir in all_temp_dirs:
        try:
            if os.path.exists(temp_dir):
                self.logger.info(f'清理临时目录: {temp_dir}')
                try:
                    # 先尝试删除所有文件
                    for root, dirs, files in os.walk(temp_dir, topdown=False):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                if os.path.exists(file_path):
                                    os.remove(file_path)
                            except Exception as e:
                                self.logger.warning(f'删除临时文件失败: {file_path}, 错误: {str(e)}')

                    # 然后删除目录
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    self.logger.info(f'临时目录已清理: {temp_dir}')

                except Exception as e:
                    self.logger.warning(f'使用常规方法删除临时目录失败，尝试使用系统命令: {str(e)}')
                    try:
                        if platform.system() == 'Windows':
                            subprocess.run(f'rd /s /q "{temp_dir}"', shell=True,
                                         stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         startupinfo=self.startupinfo,
                                         creationflags=self.creation_flags)
                        else:
                            subprocess.run(f'rm -rf "{temp_dir}"', shell=True,
                                         stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                        if not os.path.exists(temp_dir):
                            self.logger.info(f'使用系统命令成功清理临时目录: {temp_dir}')
                        else:
                            self.logger.warning(f'使用系统命令后临时目录仍存在: {temp_dir}')

                    except Exception as cmd_error:
                        self.logger.warning(f'使用系统命令删除临时目录失败: {temp_dir}, 错误: {str(cmd_error)}')
            else:
                self.logger.info(f'临时目录不存在，无需清理: {temp_dir}')

        except Exception as e:
            self.logger.warning(f'清理临时目录过程中出错: {temp_dir}, 错误: {str(e)}')

    self.temp_dirs = []
    self.logger.info('临时目录清理完成')
```

## 最佳实践与优化

### 1. 性能优化策略

#### 多线程处理
- 使用 `concurrent.futures.ThreadPoolExecutor` 实现并行处理
- 动态调整线程数量，最大不超过4个线程
- 每个任务使用独立的临时目录，避免文件冲突

#### 硬件加速
- 自动检测可用的GPU类型（NVIDIA、Intel、AMD）
- 根据GPU类型选择最优的硬件编码器
- 降级到软件编码作为备选方案

#### 内存管理
- 及时清理临时文件和目录
- 使用流式处理避免大文件占用内存
- 实现垃圾回收机制

### 2. 错误处理机制

#### 进程监控
```python
def cancel(self):
    """强制取消处理"""
    self.is_canceled = True
    self.logger.info('取消处理请求已接收，处理将在下一个安全点终止')

    try:
        if platform.system() == 'Windows':
            try:
                # 强制终止FFmpeg进程
                subprocess.run(['taskkill', '/F', '/T', '/IM', 'ffmpeg.exe'],
                             stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                             startupinfo=self.startupinfo, creationflags=self.creation_flags)
                time.sleep(1)
                subprocess.run(['taskkill', '/F', '/IM', 'ffmpeg.exe'],
                             stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                             startupinfo=self.startupinfo, creationflags=self.creation_flags)
                self.logger.info('已强制终止所有FFmpeg进程')
            except:
                pass
        else:
            try:
                subprocess.run(['killall', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                time.sleep(0.5)
                subprocess.run(['killall', '-9', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.logger.info('已强制终止所有FFmpeg进程')
            except:
                pass
    except Exception as e:
        self.logger.warning(f'尝试终止FFmpeg进程时出错: {str(e)}')

    time.sleep(2)
    self._clean_temp_dirs()
```

#### 备用方案
- 字幕添加失败时自动尝试备用命令
- 硬件编码失败时降级到软件编码
- 文件操作失败时提供详细错误信息

### 3. 日志系统

```python
class Logger:
    def __init__(self, logging_level=logging.INFO):
        self.level = logging_level
        logging.basicConfig(level=logging_level,
                          format='%(asctime)s - %(levelname)s - %(message)s',
                          datefmt='%Y-%m-%d %H:%M:%S')

    def info(self, message):
        logging.info(message)

    def warning(self, message):
        logging.warning(message)

    def error(self, message):
        logging.error(message)

    def debug(self, message):
        logging.debug(message)
```

## 总结

本文档详细介绍了基于FFmpeg的视频合成工具的核心技术实现，包括：

1. **多线程架构** - 使用线程池实现并行处理，提高处理效率
2. **硬件加速** - 自动检测并使用GPU硬件编码器
3. **视频处理** - 一步完成视频循环和音频合并
4. **音频处理** - BGM添加和音量控制
5. **字幕系统** - 支持多种样式的字幕添加
6. **进度监控** - 实时解析FFmpeg输出并显示进度
7. **错误处理** - 完善的异常处理和备用方案
8. **资源管理** - 自动清理临时文件和目录

这些技术的组合使得该工具能够高效、稳定地处理大量视频文件，同时提供良好的用户体验和错误恢复能力。
